# 🏢 نظام إدارة الشركات المحسن v2.0
## Enhanced Company Management System v2.0

<div align="center">

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Arabic](https://img.shields.io/badge/Language-Arabic-red.svg)](README.md)

**نظام شامل ومتطور لإدارة الشركات وفئات رأس المال مع ميزات أمان متقدمة ومراقبة الأداء في الوقت الفعلي**

</div>

---

## 🚀 الميزات الجديدة في الإصدار 2.0

### 🔒 الأمان المتقدم
- ✅ **تشفير قوي**: استخدام bcrypt مع salt عشوائي
- ✅ **حماية CSRF**: حماية شاملة من هجمات Cross-Site Request Forgery
- ✅ **فحص الملفات**: نظام متقدم لفحص الملفات ضد البرمجيات الضارة
- ✅ **كلمات مرور قوية**: سياسة كلمات مرور محسنة مع التحقق من القوة
- ✅ **جلسات آمنة**: إدارة جلسات محسنة مع انتهاء صلاحية ذكي
- ✅ **نظام صلاحيات**: إدارة متقدمة للأدوار والصلاحيات

### 📊 مراقبة الأداء
- ✅ **مراقبة الوقت الفعلي**: تتبع استخدام المعالج والذاكرة والقرص
- ✅ **تحليل الاستعلامات**: رصد وتحسين استعلامات قاعدة البيانات
- ✅ **إحصائيات مفصلة**: تقارير شاملة عن أداء النظام
- ✅ **تنبيهات ذكية**: إشعارات عند وجود مشاكل في الأداء

### 💾 التخزين المؤقت الذكي
- ✅ **تخزين متعدد المستويات**: ذاكرة + قاعدة بيانات
- ✅ **تنظيف تلقائي**: إزالة البيانات المنتهية الصلاحية
- ✅ **إحصائيات التخزين**: معدل النجاح والاستخدام

### 📝 نظام التسجيل المتقدم
- ✅ **تسجيل شامل**: جميع العمليات والأحداث
- ✅ **تصنيف السجلات**: أمان، أداء، تطبيق، مراجعة
- ✅ **تدوير السجلات**: إدارة تلقائية لحجم الملفات

### 🎨 واجهة محسنة
- ✅ **تصميم عصري**: واجهة مستخدم محدثة مع تأثيرات بصرية
- ✅ **تفاعل محسن**: JavaScript متقدم للتفاعل السلس
- ✅ **استجابة سريعة**: تحسينات في سرعة التحميل والاستجابة

---

## 🛠️ الميزات الأساسية

| الميزة | الوصف | الحالة |
|--------|--------|---------|
| 📊 **إدارة فئات رأس المال** | تصنيف متقدم مع حدود قابلة للتخصيص | ✅ مكتمل |
| 🏢 **إدارة الشركات** | نظام شامل لإدارة بيانات الشركات | ✅ مكتمل |
| 👥 **إدارة الأشخاص** | قاعدة بيانات متطورة للملاك والشركاء | ✅ مكتمل |
| 📁 **نظام الملفات** | رفع وإدارة آمنة للمستندات مع فحص أمني | ✅ مكتمل |
| 📈 **لوحة تحكم ذكية** | إحصائيات تفاعلية وتقارير مرئية | ✅ مكتمل |
| 🔐 **نظام الصلاحيات** | إدارة متقدمة للأدوار والصلاحيات | ✅ مكتمل |
| 🔍 **بحث متطور** | بحث سريع ودقيق عبر جميع البيانات | ✅ مكتمل |
| 📱 **تصميم متجاوب** | متوافق مع جميع الأجهزة والشاشات | ✅ مكتمل |

---

## 📋 متطلبات النظام

### الحد الأدنى
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 512 MB RAM
- **التخزين**: 100 MB مساحة فارغة
- **المتصفح**: Chrome, Firefox, Safari, Edge (الإصدارات الحديثة)

### الموصى به
- **Python**: 3.10+
- **الذاكرة**: 1 GB RAM أو أكثر
- **التخزين**: 500 MB مساحة فارغة
- **قاعدة البيانات**: PostgreSQL أو MySQL (للإنتاج)

---

## 🚀 التثبيت والتشغيل

### 1️⃣ استنساخ المشروع
```bash
git clone <repository-url>
cd company-management-system
```

### 2️⃣ إنشاء بيئة افتراضية
```bash
python -m venv venv

# على Linux/Mac
source venv/bin/activate

# على Windows
venv\Scripts\activate
```

### 3️⃣ تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4️⃣ إعداد متغيرات البيئة (اختياري)
```bash
cp .env.example .env
# قم بتحرير ملف .env وإضافة الإعدادات المطلوبة
```

### 5️⃣ تشغيل النظام المحسن
```bash
# للنظام المحسن مع جميع الميزات الجديدة
python run_enhanced.py

# أو النظام الأساسي
python run.py
```

### 6️⃣ الوصول للنظام
🌐 افتح المتصفح وانتقل إلى: **http://localhost:5000**

---

## 🔐 معلومات تسجيل الدخول

### الحساب الافتراضي
- **👤 اسم المستخدم**: `admin`
- **🔑 كلمة المرور**: `Admin@2024!`

> ⚠️ **تحذير أمني**: يجب تغيير كلمة المرور فور تسجيل الدخول الأول!

---

## 📁 هيكل المشروع المحسن

```
📦 company-management-system/
├── 📄 app.py                    # التطبيق الرئيسي المحسن
├── 📄 models.py                 # نماذج قاعدة البيانات المحسنة
├── 📄 config.py                 # إعدادات التطبيق المتقدمة
├── 📄 security.py               # نظام الأمان والحماية
├── 📄 permissions.py            # نظام الأدوار والصلاحيات
├── 📄 cache_manager.py          # إدارة التخزين المؤقت
├── 📄 performance_monitor.py    # مراقبة الأداء
├── 📄 file_scanner.py           # فحص الملفات الأمني
├── 📄 file_monitor.py           # مراقبة الملفات
├── 📄 logging_system.py         # نظام التسجيل المتقدم
├── 📄 query_optimizer.py        # محسن الاستعلامات
├── 📄 utils.py                  # دوال مساعدة محسنة
├── 📄 run_enhanced.py           # ملف التشغيل المحسن
├── 📄 requirements.txt          # متطلبات Python المحدثة
├── 📁 blueprints/               # تقسيم المسارات
│   ├── 📄 main.py              # المسارات الرئيسية
│   ├── 📄 auth.py              # المصادقة والمستخدمين
│   └── 📄 companies.py         # إدارة الشركات
├── 📁 templates/                # قوالب HTML محسنة
│   ├── 📁 auth/                # قوالب المصادقة
│   ├── 📁 companies/           # قوالب الشركات
│   └── 📁 errors/              # صفحات الأخطاء
├── 📁 static/                   # الملفات الثابتة
│   ├── 📁 css/
│   │   ├── 📄 style.css        # التنسيقات الأساسية
│   │   └── 📄 enhanced.css     # التنسيقات المحسنة
│   └── 📁 js/
│       └── 📄 main.js          # JavaScript محسن
├── 📁 uploads/                  # ملفات المرفوعات
├── 📁 logs/                     # ملفات السجلات
├── 📁 cache/                    # ملفات التخزين المؤقت
└── 📁 backups/                  # النسخ الاحتياطية
```

---

## 🎯 الاستخدام المتقدم

### 📊 لوحة المراقبة
```
🌐 http://localhost:5000/admin/monitoring
```
- مراقبة الأداء في الوقت الفعلي
- إحصائيات النظام والموارد
- تقارير الاستعلامات البطيئة

### 🔧 إعدادات النظام
```
🌐 http://localhost:5000/admin/settings
```
- تكوين الأمان والحماية
- إعدادات التخزين المؤقت
- إدارة النسخ الاحتياطية

### 📈 تقارير الأداء
```
🌐 http://localhost:5000/admin/performance
```
- تحليل أداء قاعدة البيانات
- إحصائيات الاستخدام
- اقتراحات التحسين

---

## 🔧 التكوين المتقدم

### إعدادات الأمان
```python
# في ملف .env
SECRET_KEY=your-very-secret-key-here
SECURITY_PASSWORD_SALT=your-password-salt
ENABLE_CSRF_PROTECTION=True
SESSION_TIMEOUT_HOURS=8
```

### إعدادات الأداء
```python
# في ملف config.py
CACHE_TIMEOUT = 3600  # ساعة واحدة
SLOW_QUERY_THRESHOLD = 1.0  # ثانية واحدة
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
```

---

## 🛡️ الأمان والحماية

### 🔐 حماية البيانات
- ✅ تشفير كلمات المرور باستخدام bcrypt
- ✅ حماية من SQL Injection
- ✅ تنظيف وتحقق من جميع المدخلات
- ✅ جلسات آمنة مع HTTPS

### 📁 حماية الملفات
- ✅ فحص أمني شامل للملفات المرفوعة
- ✅ قائمة سوداء للامتدادات الخطيرة
- ✅ فحص محتوى الملفات
- ✅ عزل الملفات المشبوهة

### 📊 مراقبة الأمان
- ✅ تسجيل جميع الأنشطة الأمنية
- ✅ كشف محاولات الاختراق
- ✅ تنبيهات الأمان الفورية
- ✅ تقارير أمنية دورية

---

## 🚀 التحسينات المستقبلية

### الإصدار القادم (v2.1)
- [ ] دعم قواعد بيانات متعددة (PostgreSQL, MySQL)
- [ ] API RESTful كامل
- [ ] تطبيق جوال مصاحب
- [ ] تكامل مع الخدمات السحابية
- [ ] نظام إشعارات متقدم
- [ ] تقارير PDF تلقائية
- [ ] دعم اللغات المتعددة
- [ ] نظام سير العمل (Workflow)

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. **Fork** المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. تنفيذ التغييرات مع اتباع معايير الكود
4. كتابة اختبارات للميزة الجديدة
5. إرسال **Pull Request**

### معايير المساهمة
- ✅ اتباع PEP 8 لتنسيق الكود
- ✅ كتابة تعليقات باللغة العربية
- ✅ إضافة اختبارات للميزات الجديدة
- ✅ تحديث الوثائق عند الحاجة

---

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **المنتدى**: [رابط المنتدى]
- 📱 **تليجرام**: @CompanySystemSupport
- 🐛 **الإبلاغ عن مشاكل**: [GitHub Issues]

---

## 📄 الترخيص

هذا المشروع مرخص تحت **رخصة MIT** - راجع ملف [LICENSE](LICENSE) للتفاصيل الكاملة.

---

## 🙏 شكر وتقدير

- **فريق التطوير**: شكر خاص لجميع المساهمين
- **المجتمع**: شكراً للمجتمع العربي على الدعم والتشجيع
- **المكتبات المستخدمة**: Flask, SQLAlchemy, Bootstrap وجميع المكتبات الأخرى

---

<div align="center">

**🏢 تم تطويره بـ ❤️ للمجتمع العربي**

**Enhanced Company Management System v2.0**

*نظام إدارة الشركات المحسن - الإصدار الثاني*

</div>
