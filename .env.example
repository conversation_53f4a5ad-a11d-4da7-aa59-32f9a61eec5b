# إعدادات التطبيق - Company Management System Configuration
# انسخ هذا الملف إلى .env وقم بتعديل القيم حسب الحاجة

# مفتاح الأمان (يجب تغييره في الإنتاج)
SECRET_KEY=your-very-secret-key-change-this-in-production

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///companies.db
# للاستخدام مع PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost/companies_db
# للاستخدام مع MySQL:
# DATABASE_URL=mysql://username:password@localhost/companies_db

# بيئة التطبيق
FLASK_ENV=development
FLASK_DEBUG=True

# إعدادات رفع الملفات
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# إعدادات البريد الإلكتروني (للإشعارات المستقبلية)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# إعدادات النسخ الاحتياطي
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=True

# إعدادات الأمان المتقدمة
SESSION_TIMEOUT_HOURS=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30
SECURITY_PASSWORD_SALT=your-password-salt-here
JWT_SECRET_KEY=your-jwt-secret-key-here
ENABLE_CSRF_PROTECTION=True

# إعدادات التسجيل والمراقبة
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
ENABLE_AUDIT_LOG=True

# إعدادات التطبيق
COMPANIES_PER_PAGE=10
PEOPLE_PER_PAGE=20
FILES_PER_PAGE=15
DEFAULT_LANGUAGE=ar
TIMEZONE=Asia/Riyadh

# إعدادات كلمات المرور
MIN_PASSWORD_LENGTH=8
REQUIRE_UPPERCASE=True
REQUIRE_LOWERCASE=True
REQUIRE_NUMBERS=True
REQUIRE_SPECIAL_CHARS=True

# إعدادات التخزين السحابي (اختياري)
CLOUD_STORAGE_PROVIDER=local
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-bucket-name

# إعدادات الإشعارات
ENABLE_EMAIL_NOTIFICATIONS=False
ADMIN_EMAIL=<EMAIL>