{% extends "base.html" %}

{% block title %}خطأ في الخادم - 500{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <h1 class="display-1 text-danger">500</h1>
                <h2 class="mb-4">خطأ في الخادم</h2>
                <p class="lead mb-4">
                    عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى لاحقاً.
                </p>
                <div class="mb-4">
                    <i class="bi bi-exclamation-triangle display-4 text-danger"></i>
                </div>
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                        <i class="bi bi-house"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        إعادة المحاولة
                    </button>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        إذا استمر هذا الخطأ، يرجى الاتصال بالدعم الفني
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.error-page h1 {
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-page .display-4 {
    animation: shake 0.5s infinite;
}

@keyframes shake {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}
</style>
{% endblock %}
