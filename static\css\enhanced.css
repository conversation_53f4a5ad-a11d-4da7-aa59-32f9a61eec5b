/* تنسيقات محسنة للنظام */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* تحسينات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    min-height: 100vh;
}

/* تحسينات الشريط العلوي */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: var(--box-shadow);
    border-bottom: none;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: bold;
    color: white !important;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 0.2rem;
    padding: 0.5rem 1rem !important;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-bottom: none;
    font-weight: 600;
    padding: 1rem 1.5rem;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.7) 50%, rgba(255,255,255,0.3) 100%);
}

.card-body {
    padding: 1.5rem;
}

/* تحسينات الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.6rem 1.5rem;
    position: relative;
    overflow: hidden;
    text-transform: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f39c12 100%);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    color: #212529;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #3498db 100%);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

/* تحسينات الجداول */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table {
    margin-bottom: 0;
    background: transparent;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
    position: relative;
    text-align: center;
}

.table thead th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
}

.table thead th[data-sortable="true"]:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.table tbody tr {
    transition: var(--transition);
    border-bottom: 1px solid #f8f9fa;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
}

/* تحسينات بطاقات الإحصائيات */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
}

.stat-card .stat-number {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-card .stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.stat-card .stat-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 3rem;
    opacity: 0.2;
    z-index: 0;
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
    background: white;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group-text {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 500;
}

/* تحسينات التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-right: 4px solid;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
}

.alert-success {
    border-right-color: var(--success-color);
    background: linear-gradient(135deg, rgba(212, 237, 218, 0.9) 0%, rgba(195, 230, 203, 0.9) 100%);
    color: #155724;
}

.alert-danger {
    border-right-color: var(--danger-color);
    background: linear-gradient(135deg, rgba(248, 215, 218, 0.9) 0%, rgba(245, 198, 203, 0.9) 100%);
    color: #721c24;
}

.alert-warning {
    border-right-color: var(--warning-color);
    background: linear-gradient(135deg, rgba(255, 243, 205, 0.9) 0%, rgba(255, 234, 167, 0.9) 100%);
    color: #856404;
}

.alert-info {
    border-right-color: var(--info-color);
    background: linear-gradient(135deg, rgba(209, 236, 241, 0.9) 0%, rgba(190, 229, 235, 0.9) 100%);
    color: #0c5460;
}

/* تحسينات الشارات */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 20px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.badge-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f39c12 100%);
    color: #212529;
}

.badge-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #3498db 100%);
}

/* تحسينات شريط التقدم */
.progress {
    height: 12px;
    border-radius: 20px;
    background: rgba(233, 236, 239, 0.5);
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.progress-bar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تحسينات عناصر الملفات */
.file-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 2px solid transparent;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.file-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.file-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateX(-5px);
}

.file-item:hover::before {
    transform: scaleY(1);
}

.file-icon {
    font-size: 2.5rem;
    margin-left: 1rem;
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

/* تحسينات البحث */
.search-box {
    position: relative;
}

.search-input {
    padding-right: 3rem;
    border-radius: 25px;
    border: 2px solid #e9ecef;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
}

.search-results {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid #e9ecef;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--box-shadow);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
}

.search-results a {
    display: block;
    padding: 1rem;
    text-decoration: none;
    color: var(--dark-color);
    border-bottom: 1px solid #f8f9fa;
    transition: var(--transition);
}

.search-results a:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: var(--primary-color);
    transform: translateX(-5px);
}

/* تحسينات الحركة */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.slide-in {
    animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
    from { 
        transform: translateX(-30px); 
        opacity: 0; 
    }
    to { 
        transform: translateX(0); 
        opacity: 1; 
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .stat-card .stat-number {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
        break-inside: avoid;
    }
    
    .btn {
        display: none;
    }
    
    body {
        background: white;
    }
}
