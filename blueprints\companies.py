#!/usr/bin/env python3
"""
Blueprint للشركات
Companies Blueprint
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, send_from_directory
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
import uuid
from datetime import datetime

from models import db, Company, CapitalCategory, Person, CompanyFile, AuditLog
from security import security_manager
from permissions import Permission, require_permission, can_access_resource
from utils import save_uploaded_file, validate_company_name

companies_bp = Blueprint('companies', __name__, url_prefix='/companies')

def log_company_activity(action, company, old_values=None, new_values=None):
    """تسجيل نشاط الشركة"""
    audit_log = AuditLog(
        user_id=current_user.id,
        action=action,
        resource_type='company',
        resource_id=company.id if company else None,
        old_values=old_values,
        new_values=new_values,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', '')
    )
    db.session.add(audit_log)

@companies_bp.route('/')
@login_required
@require_permission(Permission.VIEW_COMPANIES)
def companies_list():
    """قائمة الشركات"""
    page = request.args.get('page', 1, type=int)
    category_id = request.args.get('category', type=int)
    status = request.args.get('status', 'active')
    search = request.args.get('search', '').strip()
    
    query = Company.query
    
    # تطبيق الفلاتر
    if category_id:
        query = query.filter_by(capital_category_id=category_id)
    
    if status == 'active':
        query = query.filter_by(is_active=True)
    elif status == 'inactive':
        query = query.filter_by(is_active=False)
    
    if search:
        query = query.filter(Company.name.contains(search))
    
    companies = query.order_by(Company.created_at.desc()).paginate(
        page=page, per_page=current_app.config.get('COMPANIES_PER_PAGE', 10), error_out=False
    )
    
    categories = CapitalCategory.query.filter_by(is_active=True).all()
    
    return render_template('companies/list.html', 
                         companies=companies, 
                         categories=categories,
                         current_category=category_id,
                         current_status=status,
                         search_query=search)

@companies_bp.route('/<int:company_id>')
@login_required
@require_permission(Permission.VIEW_COMPANIES)
def company_details(company_id):
    """تفاصيل الشركة"""
    company = Company.query.get_or_404(company_id)
    
    # تسجيل عرض الشركة
    log_company_activity('view', company)
    
    return render_template('companies/details.html', company=company)

@companies_bp.route('/add/<int:category_id>', methods=['GET', 'POST'])
@login_required
@require_permission(Permission.CREATE_COMPANY)
def add_company(category_id):
    """إضافة شركة جديدة"""
    category = CapitalCategory.query.get_or_404(category_id)
    
    if not category.can_add_company:
        flash(f'لا يمكن إضافة شركة جديدة. تم الوصول للحد الأقصى ({category.max_companies}) في هذه الفئة', 'error')
        return redirect(url_for('categories.category_companies', category_id=category_id))
    
    if request.method == 'POST':
        try:
            # بيانات الشركة
            company_name = request.form.get('company_name', '').strip()
            project_plan = request.form.get('project_plan', '').strip()
            resources = request.form.get('resources', '').strip()
            work_method = request.form.get('work_method', '').strip()
            registration_number = request.form.get('registration_number', '').strip()
            license_number = request.form.get('license_number', '').strip()
            
            # بيانات المالك الأساسي
            owner_name = request.form.get('owner_name', '').strip()
            owner_national_id = request.form.get('owner_national_id', '').strip()
            owner_phone = request.form.get('owner_phone', '').strip()
            owner_email = request.form.get('owner_email', '').strip()
            owner_address = request.form.get('owner_address', '').strip()
            
            # التحقق من البيانات الأساسية
            if not all([company_name, project_plan, owner_name]):
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return render_template('companies/add.html', category=category)
            
            # التحقق من صحة اسم الشركة
            is_valid, error_msg = validate_company_name(company_name)
            if not is_valid:
                flash(error_msg, 'error')
                return render_template('companies/add.html', category=category)
            
            # التحقق من عدم تكرار اسم الشركة
            existing_company = Company.query.filter_by(name=company_name, is_active=True).first()
            if existing_company:
                flash('يوجد شركة بنفس الاسم مسبقاً', 'error')
                return render_template('companies/add.html', category=category)
            
            # التحقق من وجود المالك مسبقاً
            owner = None
            if owner_national_id:
                owner = Person.query.filter_by(national_id=owner_national_id).first()
            
            if not owner and owner_email:
                owner = Person.query.filter_by(email=owner_email).first()
            
            if not owner:
                # إنشاء مالك جديد
                owner = Person(
                    name=owner_name,
                    national_id=owner_national_id if owner_national_id else None,
                    phone=owner_phone,
                    email=owner_email,
                    address=owner_address
                )
                db.session.add(owner)
                db.session.flush()  # للحصول على ID المالك
            
            # إنشاء الشركة
            company = Company(
                name=company_name,
                project_plan=project_plan,
                resources=resources,
                work_method=work_method,
                registration_number=registration_number if registration_number else None,
                license_number=license_number if license_number else None,
                capital_category_id=category_id,
                primary_owner_id=owner.id
            )
            
            db.session.add(company)
            db.session.commit()
            
            # تسجيل النشاط
            log_company_activity('create', company, 
                               new_values=f'{{"name": "{company_name}", "category": "{category.name}"}}')
            db.session.commit()
            
            flash(f'تم إضافة الشركة "{company_name}" بنجاح', 'success')
            return redirect(url_for('companies.company_details', company_id=company.id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating company: {str(e)}")
            flash(f'حدث خطأ في إضافة الشركة: {str(e)}', 'error')
    
    return render_template('companies/add.html', category=category)

@companies_bp.route('/<int:company_id>/edit', methods=['GET', 'POST'])
@login_required
@require_permission(Permission.EDIT_COMPANY)
def edit_company(company_id):
    """تعديل الشركة"""
    company = Company.query.get_or_404(company_id)
    
    if request.method == 'POST':
        try:
            # حفظ القيم القديمة للمراجعة
            old_values = {
                'name': company.name,
                'project_plan': company.project_plan,
                'resources': company.resources,
                'work_method': company.work_method,
                'status': company.status
            }
            
            # تحديث البيانات
            company.name = request.form.get('name', '').strip()
            company.project_plan = request.form.get('project_plan', '').strip()
            company.resources = request.form.get('resources', '').strip()
            company.work_method = request.form.get('work_method', '').strip()
            company.status = request.form.get('status', 'active')
            
            # التحقق من صحة البيانات
            if not all([company.name, company.project_plan]):
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return render_template('companies/edit.html', company=company)
            
            db.session.commit()
            
            # تسجيل النشاط
            new_values = {
                'name': company.name,
                'project_plan': company.project_plan,
                'resources': company.resources,
                'work_method': company.work_method,
                'status': company.status
            }
            
            log_company_activity('update', company, 
                               old_values=str(old_values),
                               new_values=str(new_values))
            db.session.commit()
            
            flash(f'تم تحديث الشركة "{company.name}" بنجاح', 'success')
            return redirect(url_for('companies.company_details', company_id=company.id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating company: {str(e)}")
            flash(f'حدث خطأ في تحديث الشركة: {str(e)}', 'error')
    
    return render_template('companies/edit.html', company=company)

@companies_bp.route('/<int:company_id>/delete', methods=['POST'])
@login_required
@require_permission(Permission.DELETE_COMPANY)
def delete_company(company_id):
    """حذف الشركة"""
    company = Company.query.get_or_404(company_id)
    
    try:
        # التحقق من إمكانية الحذف
        if not company.can_be_deleted():
            flash('لا يمكن حذف الشركة لأنها تحتوي على ملفات أو شركاء', 'error')
            return redirect(url_for('companies.company_details', company_id=company_id))
        
        company_name = company.name
        category_id = company.capital_category_id
        
        # حذف ناعم
        company.is_active = False
        
        # تسجيل النشاط
        log_company_activity('delete', company, 
                           old_values=f'{{"name": "{company_name}", "is_active": true}}',
                           new_values=f'{{"name": "{company_name}", "is_active": false}}')
        
        db.session.commit()
        
        flash(f'تم حذف الشركة "{company_name}" بنجاح', 'success')
        return redirect(url_for('categories.category_companies', category_id=category_id))
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting company: {str(e)}")
        flash(f'حدث خطأ في حذف الشركة: {str(e)}', 'error')
        return redirect(url_for('companies.company_details', company_id=company_id))

@companies_bp.route('/<int:company_id>/upload', methods=['POST'])
@login_required
@require_permission(Permission.UPLOAD_FILES)
def upload_file(company_id):
    """رفع ملف للشركة"""
    company = Company.query.get_or_404(company_id)
    
    if 'file' not in request.files:
        flash('لم يتم اختيار ملف', 'error')
        return redirect(url_for('companies.company_details', company_id=company_id))
    
    file = request.files['file']
    description = request.form.get('description', '').strip()
    
    if file.filename == '':
        flash('لم يتم اختيار ملف', 'error')
        return redirect(url_for('companies.company_details', company_id=company_id))
    
    try:
        # التحقق من أمان الملف
        if not security_manager.is_safe_file(file.filename):
            flash('نوع الملف غير مسموح أو خطير', 'error')
            return redirect(url_for('companies.company_details', company_id=company_id))
        
        file_info = save_uploaded_file(file, company_id)
        if file_info:
            company_file = CompanyFile(
                company_id=company_id,
                filename=file_info['filename'],
                original_filename=file_info['original_filename'],
                file_path=file_info['file_path'],
                file_type=file_info['file_type'],
                file_size=file_info['file_size'],
                mime_type=file_info['mime_type'],
                file_hash=file_info.get('file_hash'),
                description=description,
                uploaded_by=current_user.id
            )
            
            db.session.add(company_file)
            
            # تسجيل النشاط
            log_company_activity('upload', company,
                               new_values=f'{{"filename": "{file_info["original_filename"]}", "size": {file_info["file_size"]}}}')
            
            db.session.commit()
            
            flash('تم رفع الملف بنجاح', 'success')
        else:
            flash('فشل في رفع الملف', 'error')
            
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error uploading file: {str(e)}")
        flash(f'حدث خطأ في رفع الملف: {str(e)}', 'error')
    
    return redirect(url_for('companies.company_details', company_id=company_id))
