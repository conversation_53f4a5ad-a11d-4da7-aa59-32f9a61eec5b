#!/usr/bin/env python3
"""
تشغيل نظام إدارة الشركات المحسن
Enhanced Company Management System Runner
"""

import os
import sys
import signal
import threading
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from app import create_app
from models import db, User, CapitalCategory
from cache_manager import cache_manager
from performance_monitor import performance_monitor
from file_monitor import file_monitor
from logging_system import logger_system

def create_default_data():
    """إنشاء البيانات الافتراضية"""
    
    # إنشاء المستخدم الافتراضي
    if not User.query.first():
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True
        )
        admin_user.set_password('Admin@2024!')  # كلمة مرور قوية
        db.session.add(admin_user)
        print("✅ تم إنشاء المستخدم الافتراضي: admin / Admin@2024!")
    
    # إنشاء فئات رأس المال الافتراضية
    if not CapitalCategory.query.first():
        categories = [
            {
                'name': '10,000 ريال سعودي',
                'amount': 10000,
                'max_companies': 15,
                'description': 'فئة رأس المال الصغير - مناسبة للمشاريع الناشئة'
            },
            {
                'name': '50,000 ريال سعودي',
                'amount': 50000,
                'max_companies': 12,
                'description': 'فئة رأس المال المتوسط - مناسبة للشركات الصغيرة'
            },
            {
                'name': '100,000 ريال سعودي',
                'amount': 100000,
                'max_companies': 8,
                'description': 'فئة رأس المال الكبير - مناسبة للشركات المتوسطة'
            },
            {
                'name': '500,000 ريال سعودي',
                'amount': 500000,
                'max_companies': 5,
                'description': 'فئة رأس المال الكبير جداً - مناسبة للشركات الكبيرة'
            },
            {
                'name': '1,000,000 ريال سعودي',
                'amount': 1000000,
                'max_companies': 3,
                'description': 'فئة رأس المال الضخم - مناسبة للمؤسسات الكبرى'
            }
        ]
        
        for cat_data in categories:
            category = CapitalCategory(**cat_data)
            db.session.add(category)
        
        print("✅ تم إنشاء فئات رأس المال الافتراضية")
    
    db.session.commit()

def setup_monitoring():
    """إعداد أنظمة المراقبة"""
    try:
        # بدء مراقبة الأداء
        performance_monitor.start_monitoring(interval=60)  # كل دقيقة
        print("✅ تم بدء مراقبة الأداء")
        
        # بدء مراقبة الملفات
        file_monitor.start_monitoring()
        print("✅ تم بدء مراقبة الملفات")
        
        return True
    except Exception as e:
        print(f"⚠️ خطأ في إعداد المراقبة: {e}")
        return False

def cleanup_on_exit():
    """تنظيف الموارد عند الإغلاق"""
    print("\n🔄 جاري إيقاف الخدمات...")
    
    try:
        # إيقاف مراقبة الأداء
        performance_monitor.stop_monitoring()
        print("✅ تم إيقاف مراقبة الأداء")
        
        # إيقاف مراقبة الملفات
        file_monitor.stop_monitoring()
        print("✅ تم إيقاف مراقبة الملفات")
        
        # تنظيف التخزين المؤقت
        cache_manager.cleanup_expired()
        print("✅ تم تنظيف التخزين المؤقت")
        
    except Exception as e:
        print(f"⚠️ خطأ في التنظيف: {e}")
    
    print("👋 تم إيقاف النظام بنجاح")

def signal_handler(signum, frame):
    """معالج إشارات النظام"""
    cleanup_on_exit()
    sys.exit(0)

def print_system_info():
    """طباعة معلومات النظام"""
    print("=" * 60)
    print("🏢 نظام إدارة الشركات المحسن v2.0")
    print("Enhanced Company Management System v2.0")
    print("=" * 60)
    print("🚀 الميزات الجديدة:")
    print("   • نظام أمان محسن مع تشفير قوي")
    print("   • مراقبة الأداء في الوقت الفعلي")
    print("   • فحص الملفات ضد البرمجيات الضارة")
    print("   • تخزين مؤقت ذكي لتحسين الأداء")
    print("   • نظام تسجيل متقدم للأحداث")
    print("   • واجهة مستخدم محسنة وتفاعلية")
    print("   • نظام صلاحيات متقدم")
    print("   • تحسين الاستعلامات تلقائياً")
    print("=" * 60)
    print(f"🌐 الخادم يعمل على: http://localhost:5000")
    print(f"👤 المستخدم الافتراضي: admin")
    print(f"🔑 كلمة المرور الافتراضية: Admin@2024!")
    print("⚠️  يرجى تغيير كلمة المرور فور تسجيل الدخول!")
    print("=" * 60)
    print("📊 لوحة المراقبة: http://localhost:5000/admin/monitoring")
    print("🔧 إعدادات النظام: http://localhost:5000/admin/settings")
    print("📈 تقارير الأداء: http://localhost:5000/admin/performance")
    print("=" * 60)

def check_system_requirements():
    """فحص متطلبات النظام"""
    print("🔍 فحص متطلبات النظام...")
    
    # فحص Python version
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    # فحص المجلدات المطلوبة
    required_dirs = ['logs', 'uploads', 'backups', 'cache']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def main():
    """الدالة الرئيسية"""
    
    # فحص متطلبات النظام
    if not check_system_requirements():
        sys.exit(1)
    
    # إعداد معالجات الإشارات
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # إنشاء التطبيق
    app = create_app()
    
    with app.app_context():
        try:
            # إنشاء قاعدة البيانات
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")
            
            # إنشاء البيانات الافتراضية
            create_default_data()
            
            # إعداد أنظمة المراقبة
            setup_monitoring()
            
            # طباعة معلومات النظام
            print_system_info()
            
            # تشغيل الخادم
            app.run(
                host='0.0.0.0',
                port=int(os.environ.get('PORT', 5000)),
                debug=os.environ.get('FLASK_ENV') == 'development',
                use_reloader=False,  # تعطيل reloader لتجنب تشغيل المراقبة مرتين
                threaded=True
            )
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
        except Exception as e:
            print(f"❌ خطأ في تشغيل الخادم: {e}")
        finally:
            cleanup_on_exit()

if __name__ == '__main__':
    main()
