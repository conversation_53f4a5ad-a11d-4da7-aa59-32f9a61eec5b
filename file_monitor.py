#!/usr/bin/env python3
"""
نظام مراقبة الملفات
File Monitoring System
"""

import os
import time
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
import json
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class FileMonitor:
    """مراقب الملفات"""
    
    def __init__(self, upload_dir='uploads'):
        self.upload_dir = Path(upload_dir)
        self.monitor_log = self.upload_dir / 'monitor.log'
        self.file_registry = self.upload_dir / 'file_registry.json'
        self.observer = None
        self.running = False
        
        # إنشاء المجلدات المطلوبة
        self.upload_dir.mkdir(exist_ok=True)
        
        # تحميل سجل الملفات
        self.registry = self._load_registry()
    
    def _load_registry(self):
        """تحميل سجل الملفات"""
        try:
            if self.file_registry.exists():
                with open(self.file_registry, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self._log(f"خطأ في تحميل سجل الملفات: {e}")
        
        return {}
    
    def _save_registry(self):
        """حفظ سجل الملفات"""
        try:
            with open(self.file_registry, 'w', encoding='utf-8') as f:
                json.dump(self.registry, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self._log(f"خطأ في حفظ سجل الملفات: {e}")
    
    def _log(self, message):
        """تسجيل رسالة في سجل المراقبة"""
        timestamp = datetime.now().isoformat()
        log_entry = f"[{timestamp}] {message}\n"
        
        try:
            with open(self.monitor_log, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception:
            pass  # تجاهل أخطاء التسجيل
        
        print(f"FileMonitor: {message}")
    
    def register_file(self, file_path, metadata=None):
        """تسجيل ملف جديد في السجل"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return False
            
            # حساب معلومات الملف
            file_stat = file_path.stat()
            file_hash = self._calculate_file_hash(file_path)
            
            file_info = {
                'path': str(file_path),
                'size': file_stat.st_size,
                'hash': file_hash,
                'created': datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                'registered': datetime.now().isoformat(),
                'metadata': metadata or {}
            }
            
            self.registry[str(file_path)] = file_info
            self._save_registry()
            
            self._log(f"تم تسجيل الملف: {file_path}")
            return True
            
        except Exception as e:
            self._log(f"خطأ في تسجيل الملف {file_path}: {e}")
            return False
    
    def _calculate_file_hash(self, file_path):
        """حساب hash للملف"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception:
            return None
    
    def verify_file_integrity(self, file_path):
        """التحقق من سلامة الملف"""
        file_path = str(file_path)
        
        if file_path not in self.registry:
            return {'status': 'unknown', 'message': 'الملف غير مسجل'}
        
        try:
            if not Path(file_path).exists():
                return {'status': 'missing', 'message': 'الملف مفقود'}
            
            current_hash = self._calculate_file_hash(file_path)
            registered_hash = self.registry[file_path]['hash']
            
            if current_hash == registered_hash:
                return {'status': 'intact', 'message': 'الملف سليم'}
            else:
                return {'status': 'modified', 'message': 'تم تعديل الملف'}
                
        except Exception as e:
            return {'status': 'error', 'message': f'خطأ في التحقق: {e}'}
    
    def scan_directory(self):
        """فحص المجلد للبحث عن تغييرات"""
        changes = {
            'new_files': [],
            'modified_files': [],
            'missing_files': [],
            'corrupted_files': []
        }
        
        try:
            # فحص الملفات الموجودة
            for file_path in self.upload_dir.rglob('*'):
                if file_path.is_file():
                    file_str = str(file_path)
                    
                    if file_str not in self.registry:
                        changes['new_files'].append(file_str)
                    else:
                        integrity = self.verify_file_integrity(file_str)
                        if integrity['status'] == 'modified':
                            changes['modified_files'].append(file_str)
                        elif integrity['status'] == 'error':
                            changes['corrupted_files'].append(file_str)
            
            # فحص الملفات المسجلة للبحث عن المفقودة
            for file_path in list(self.registry.keys()):
                if not Path(file_path).exists():
                    changes['missing_files'].append(file_path)
            
            return changes
            
        except Exception as e:
            self._log(f"خطأ في فحص المجلد: {e}")
            return changes
    
    def cleanup_old_files(self, days_old=30):
        """تنظيف الملفات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        cleaned_files = []
        
        try:
            for file_path, file_info in list(self.registry.items()):
                created_date = datetime.fromisoformat(file_info['created'])
                
                if created_date < cutoff_date:
                    try:
                        if Path(file_path).exists():
                            os.remove(file_path)
                        
                        del self.registry[file_path]
                        cleaned_files.append(file_path)
                        
                    except Exception as e:
                        self._log(f"خطأ في حذف الملف {file_path}: {e}")
            
            if cleaned_files:
                self._save_registry()
                self._log(f"تم تنظيف {len(cleaned_files)} ملف قديم")
            
            return cleaned_files
            
        except Exception as e:
            self._log(f"خطأ في تنظيف الملفات: {e}")
            return []
    
    def get_storage_stats(self):
        """إحصائيات التخزين"""
        try:
            total_size = 0
            file_count = 0
            
            for file_path in self.upload_dir.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
            
            return {
                'total_files': file_count,
                'total_size': total_size,
                'total_size_formatted': self._format_size(total_size),
                'registered_files': len(self.registry),
                'upload_dir': str(self.upload_dir)
            }
            
        except Exception as e:
            self._log(f"خطأ في حساب إحصائيات التخزين: {e}")
            return {}
    
    def _format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def start_monitoring(self):
        """بدء مراقبة الملفات في الوقت الفعلي"""
        if self.running:
            return False
        
        try:
            event_handler = FileChangeHandler(self)
            self.observer = Observer()
            self.observer.schedule(event_handler, str(self.upload_dir), recursive=True)
            self.observer.start()
            self.running = True
            
            self._log("تم بدء مراقبة الملفات")
            return True
            
        except Exception as e:
            self._log(f"خطأ في بدء المراقبة: {e}")
            return False
    
    def stop_monitoring(self):
        """إيقاف مراقبة الملفات"""
        if not self.running or not self.observer:
            return False
        
        try:
            self.observer.stop()
            self.observer.join()
            self.running = False
            
            self._log("تم إيقاف مراقبة الملفات")
            return True
            
        except Exception as e:
            self._log(f"خطأ في إيقاف المراقبة: {e}")
            return False

class FileChangeHandler(FileSystemEventHandler):
    """معالج تغييرات الملفات"""
    
    def __init__(self, monitor):
        self.monitor = monitor
    
    def on_created(self, event):
        if not event.is_directory:
            self.monitor._log(f"تم إنشاء ملف جديد: {event.src_path}")
    
    def on_modified(self, event):
        if not event.is_directory:
            self.monitor._log(f"تم تعديل ملف: {event.src_path}")
    
    def on_deleted(self, event):
        if not event.is_directory:
            self.monitor._log(f"تم حذف ملف: {event.src_path}")
            # إزالة الملف من السجل
            if event.src_path in self.monitor.registry:
                del self.monitor.registry[event.src_path]
                self.monitor._save_registry()

# إنشاء مثيل عام من مراقب الملفات
file_monitor = FileMonitor()
