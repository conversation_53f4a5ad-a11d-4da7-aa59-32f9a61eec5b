#!/usr/bin/env python3
"""
نظام تحسين الاستعلامات
Query Optimization System
"""

from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Query
from flask import current_app
import time
import logging
from functools import wraps
from collections import defaultdict, deque
from datetime import datetime, timedelta

class QueryOptimizer:
    """محسن الاستعلامات"""
    
    def __init__(self):
        self.query_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'avg_time': 0,
            'max_time': 0,
            'min_time': float('inf'),
            'last_executed': None
        })
        self.slow_queries = deque(maxlen=100)  # آخر 100 استعلام بطيء
        self.query_cache = {}
        self.optimization_rules = []
        
        # إعدادات التحسين
        self.slow_query_threshold = 1.0  # ثانية واحدة
        self.cache_timeout = 300  # 5 دقائق
        
        # تسجيل الاستعلامات
        self.logger = logging.getLogger('query_optimizer')
    
    def register_optimization_rule(self, rule_func):
        """تسجيل قاعدة تحسين جديدة"""
        self.optimization_rules.append(rule_func)
    
    def analyze_query(self, query_text, execution_time, parameters=None):
        """تحليل الاستعلام وتسجيل الإحصائيات"""
        # تنظيف النص للحصول على نمط الاستعلام
        query_pattern = self._normalize_query(query_text)
        
        # تحديث الإحصائيات
        stats = self.query_stats[query_pattern]
        stats['count'] += 1
        stats['total_time'] += execution_time
        stats['avg_time'] = stats['total_time'] / stats['count']
        stats['max_time'] = max(stats['max_time'], execution_time)
        stats['min_time'] = min(stats['min_time'], execution_time)
        stats['last_executed'] = datetime.now()
        
        # تسجيل الاستعلامات البطيئة
        if execution_time > self.slow_query_threshold:
            slow_query_info = {
                'query': query_text,
                'pattern': query_pattern,
                'execution_time': execution_time,
                'parameters': parameters,
                'timestamp': datetime.now()
            }
            self.slow_queries.append(slow_query_info)
            
            # تسجيل في السجل
            self.logger.warning(
                f"استعلام بطيء: {execution_time:.3f}s - {query_pattern[:100]}..."
            )
    
    def _normalize_query(self, query_text):
        """تطبيع نص الاستعلام لإنشاء نمط"""
        import re
        
        # إزالة المسافات الزائدة والأسطر الجديدة
        normalized = re.sub(r'\s+', ' ', query_text.strip())
        
        # استبدال القيم بـ placeholders
        normalized = re.sub(r"'[^']*'", "'?'", normalized)  # النصوص
        normalized = re.sub(r'\b\d+\b', '?', normalized)    # الأرقام
        
        # توحيد الحالة
        normalized = normalized.upper()
        
        return normalized
    
    def get_query_suggestions(self, query_text):
        """الحصول على اقتراحات لتحسين الاستعلام"""
        suggestions = []
        
        # تطبيق قواعد التحسين
        for rule in self.optimization_rules:
            try:
                suggestion = rule(query_text)
                if suggestion:
                    suggestions.append(suggestion)
            except Exception as e:
                self.logger.error(f"خطأ في قاعدة التحسين: {e}")
        
        return suggestions
    
    def get_slow_queries_report(self, limit=20):
        """تقرير الاستعلامات البطيئة"""
        # ترتيب حسب وقت التنفيذ
        sorted_queries = sorted(
            self.slow_queries,
            key=lambda x: x['execution_time'],
            reverse=True
        )
        
        return sorted_queries[:limit]
    
    def get_query_stats_report(self, limit=20):
        """تقرير إحصائيات الاستعلامات"""
        # ترتيب حسب متوسط وقت التنفيذ
        sorted_stats = sorted(
            self.query_stats.items(),
            key=lambda x: x[1]['avg_time'],
            reverse=True
        )
        
        report = []
        for pattern, stats in sorted_stats[:limit]:
            report.append({
                'pattern': pattern[:200] + '...' if len(pattern) > 200 else pattern,
                'count': stats['count'],
                'avg_time': round(stats['avg_time'], 3),
                'max_time': round(stats['max_time'], 3),
                'total_time': round(stats['total_time'], 3),
                'last_executed': stats['last_executed']
            })
        
        return report
    
    def optimize_query(self, query):
        """تحسين الاستعلام"""
        if hasattr(query, 'statement'):
            # SQLAlchemy Query object
            return self._optimize_sqlalchemy_query(query)
        else:
            # Raw SQL string
            return self._optimize_raw_query(query)
    
    def _optimize_sqlalchemy_query(self, query):
        """تحسين استعلام SQLAlchemy"""
        # إضافة eager loading للعلاقات المستخدمة بكثرة
        # يمكن تطوير هذا بناءً على أنماط الاستخدام
        
        # مثال: إضافة joinedload للعلاقات الشائعة
        from sqlalchemy.orm import joinedload
        
        # هذا مثال بسيط - يمكن تطويره ليكون أكثر ذكاءً
        if 'company' in str(query.statement).lower():
            # إضافة تحميل العلاقات المرتبطة
            pass
        
        return query
    
    def _optimize_raw_query(self, query_text):
        """تحسين الاستعلام النصي"""
        optimized = query_text
        
        # قواعد تحسين بسيطة
        suggestions = self.get_query_suggestions(query_text)
        
        # تطبيق بعض التحسينات التلقائية
        for suggestion in suggestions:
            if suggestion.get('auto_apply', False):
                optimized = suggestion['optimized_query']
                break
        
        return optimized

# قواعد التحسين الافتراضية
def missing_index_rule(query_text):
    """قاعدة اكتشاف الفهارس المفقودة"""
    import re
    
    # البحث عن WHERE clauses بدون فهارس
    where_patterns = re.findall(r'WHERE\s+(\w+)\s*=', query_text, re.IGNORECASE)
    
    suggestions = []
    for column in where_patterns:
        if column.lower() not in ['id', 'primary_key']:  # تجاهل المفاتيح الأساسية
            suggestions.append({
                'type': 'missing_index',
                'message': f'قد تحتاج إلى فهرس على العمود: {column}',
                'column': column,
                'auto_apply': False
            })
    
    return suggestions[0] if suggestions else None

def select_star_rule(query_text):
    """قاعدة تجنب SELECT *"""
    if 'SELECT *' in query_text.upper():
        return {
            'type': 'select_optimization',
            'message': 'تجنب استخدام SELECT * واختر الأعمدة المطلوبة فقط',
            'auto_apply': False
        }
    return None

def limit_rule(query_text):
    """قاعدة إضافة LIMIT للاستعلامات الكبيرة"""
    if ('SELECT' in query_text.upper() and 
        'LIMIT' not in query_text.upper() and 
        'COUNT' not in query_text.upper()):
        return {
            'type': 'limit_suggestion',
            'message': 'فكر في إضافة LIMIT لتحديد عدد النتائج',
            'auto_apply': False
        }
    return None

def join_optimization_rule(query_text):
    """قاعدة تحسين الـ JOINs"""
    join_count = query_text.upper().count('JOIN')
    if join_count > 3:
        return {
            'type': 'join_optimization',
            'message': f'الاستعلام يحتوي على {join_count} JOINs - فكر في تحسين البنية',
            'auto_apply': False
        }
    return None

# إنشاء مثيل عام من محسن الاستعلامات
query_optimizer = QueryOptimizer()

# تسجيل قواعد التحسين الافتراضية
query_optimizer.register_optimization_rule(missing_index_rule)
query_optimizer.register_optimization_rule(select_star_rule)
query_optimizer.register_optimization_rule(limit_rule)
query_optimizer.register_optimization_rule(join_optimization_rule)

# ديكوريتر لتحسين الاستعلامات
def optimize_query(func):
    """ديكوريتر لتحسين الاستعلامات تلقائياً"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # تحليل الاستعلام إذا كان متاحاً
            if hasattr(result, 'statement'):
                query_text = str(result.statement)
                query_optimizer.analyze_query(query_text, execution_time)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            query_optimizer.logger.error(f"خطأ في الاستعلام بعد {execution_time:.3f}s: {e}")
            raise
    
    return wrapper

# إعداد مراقبة الاستعلامات على مستوى SQLAlchemy
@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """مراقبة بداية تنفيذ الاستعلام"""
    context._query_start_time = time.time()

@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """مراقبة انتهاء تنفيذ الاستعلام"""
    if hasattr(context, '_query_start_time'):
        execution_time = time.time() - context._query_start_time
        query_optimizer.analyze_query(statement, execution_time, parameters)

# دوال مساعدة للتحليل
def get_database_performance_report():
    """تقرير أداء قاعدة البيانات"""
    return {
        'slow_queries': query_optimizer.get_slow_queries_report(),
        'query_stats': query_optimizer.get_query_stats_report(),
        'total_queries': sum(stats['count'] for stats in query_optimizer.query_stats.values()),
        'avg_query_time': sum(stats['avg_time'] * stats['count'] for stats in query_optimizer.query_stats.values()) / max(sum(stats['count'] for stats in query_optimizer.query_stats.values()), 1)
    }

def analyze_table_usage():
    """تحليل استخدام الجداول"""
    table_usage = defaultdict(int)
    
    for pattern, stats in query_optimizer.query_stats.items():
        # استخراج أسماء الجداول من الاستعلامات
        import re
        tables = re.findall(r'FROM\s+(\w+)', pattern, re.IGNORECASE)
        tables.extend(re.findall(r'JOIN\s+(\w+)', pattern, re.IGNORECASE))
        
        for table in tables:
            table_usage[table.lower()] += stats['count']
    
    return dict(sorted(table_usage.items(), key=lambda x: x[1], reverse=True))

def suggest_database_optimizations():
    """اقتراح تحسينات قاعدة البيانات"""
    suggestions = []
    
    # تحليل الاستعلامات البطيئة
    slow_queries = query_optimizer.get_slow_queries_report(10)
    if slow_queries:
        suggestions.append({
            'type': 'slow_queries',
            'message': f'يوجد {len(slow_queries)} استعلام بطيء يحتاج تحسين',
            'details': slow_queries[:5]  # أول 5 فقط
        })
    
    # تحليل استخدام الجداول
    table_usage = analyze_table_usage()
    most_used_tables = list(table_usage.items())[:5]
    
    if most_used_tables:
        suggestions.append({
            'type': 'table_usage',
            'message': 'الجداول الأكثر استخداماً تحتاج فهارس محسنة',
            'details': most_used_tables
        })
    
    # اقتراحات عامة
    total_queries = sum(stats['count'] for stats in query_optimizer.query_stats.values())
    if total_queries > 1000:
        avg_time = sum(stats['avg_time'] * stats['count'] for stats in query_optimizer.query_stats.values()) / total_queries
        
        if avg_time > 0.5:  # أكثر من نصف ثانية
            suggestions.append({
                'type': 'general_performance',
                'message': f'متوسط وقت الاستعلام مرتفع: {avg_time:.3f}s',
                'recommendation': 'فكر في إضافة فهارس أو تحسين بنية قاعدة البيانات'
            })
    
    return suggestions
