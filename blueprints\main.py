#!/usr/bin/env python3
"""
Blueprint الرئيسي
Main Blueprint
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime
from sqlalchemy import func

from models import db, CapitalCategory, Company, Person, CompanyFile
from permissions import Permission, require_permission

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم"""
    try:
        # إحصائيات سريعة مع استعلامات محسنة
        stats_query = db.session.query(
            func.count(CapitalCategory.id.distinct()).filter(CapitalCategory.is_active == True).label('total_categories'),
            func.count(Company.id.distinct()).filter(Company.is_active == True).label('total_companies'),
            func.count(Person.id.distinct()).filter(Person.is_active == True).label('total_people')
        ).first()
        
        total_categories = stats_query.total_categories or 0
        total_companies = stats_query.total_companies or 0
        total_people = stats_query.total_people or 0
        
        # حساب الأماكن المتاحة
        available_slots = 0
        categories = CapitalCategory.query.filter_by(is_active=True).all()
        for category in categories:
            available_slots += max(0, category.max_companies - category.current_companies_count)
        
        # أحدث الشركات
        recent_companies = Company.query.filter_by(is_active=True)\
            .order_by(Company.created_at.desc())\
            .limit(5).all()
        
        # فئات رأس المال مع إحصائياتها
        categories_stats = []
        for category in categories:
            companies_count = category.current_companies_count
            categories_stats.append({
                'category': category,
                'companies_count': companies_count,
                'available_slots': category.max_companies - companies_count,
                'usage_percentage': category.usage_percentage
            })
        
        # إحصائيات إضافية
        recent_files_count = CompanyFile.query.filter(
            CompanyFile.uploaded_at >= datetime.now().replace(day=1)
        ).count()
        
        return render_template('dashboard.html',
                             total_categories=total_categories,
                             total_companies=total_companies,
                             total_people=total_people,
                             available_slots=available_slots,
                             recent_companies=recent_companies,
                             categories_stats=categories_stats,
                             recent_files_count=recent_files_count,
                             current_date=datetime.now())
    
    except Exception as e:
        current_app.logger.error(f"Error in dashboard: {str(e)}")
        flash('حدث خطأ في تحميل لوحة التحكم', 'error')
        return render_template('dashboard.html',
                             total_categories=0,
                             total_companies=0,
                             total_people=0,
                             available_slots=0,
                             recent_companies=[],
                             categories_stats=[],
                             recent_files_count=0,
                             current_date=datetime.now())

@main_bp.route('/api/search')
@login_required
def api_search():
    """API للبحث السريع"""
    query = request.args.get('q', '').strip()
    if len(query) < 2:
        return jsonify([])
    
    results = []
    
    try:
        # البحث في فئات رأس المال
        if current_user.has_permission(Permission.VIEW_CATEGORIES):
            categories = CapitalCategory.query.filter(
                CapitalCategory.name.contains(query),
                CapitalCategory.is_active == True
            ).limit(3).all()
            
            for category in categories:
                results.append({
                    'title': category.name,
                    'description': f'فئة رأس مال - {category.description or ""}',
                    'url': url_for('categories.category_companies', category_id=category.id),
                    'type': 'category'
                })
        
        # البحث في الشركات
        if current_user.has_permission(Permission.VIEW_COMPANIES):
            companies = Company.query.filter(
                Company.name.contains(query),
                Company.is_active == True
            ).limit(3).all()
            
            for company in companies:
                results.append({
                    'title': company.name,
                    'description': f'شركة - {company.capital_category.name}',
                    'url': url_for('companies.company_details', company_id=company.id),
                    'type': 'company'
                })
        
        # البحث في الأشخاص
        if current_user.has_permission(Permission.VIEW_PEOPLE):
            people = Person.query.filter(
                Person.name.contains(query),
                Person.is_active == True
            ).limit(3).all()
            
            for person in people:
                results.append({
                    'title': person.name,
                    'description': f'شخص - {person.phone or "لا يوجد هاتف"}',
                    'url': url_for('people.person_details', person_id=person.id),
                    'type': 'person'
                })
        
        return jsonify(results[:10])  # أقصى 10 نتائج
    
    except Exception as e:
        current_app.logger.error(f"Error in search API: {str(e)}")
        return jsonify([])

@main_bp.route('/api/stats')
@login_required
def api_stats():
    """API للإحصائيات"""
    try:
        stats = {
            'total_categories': CapitalCategory.query.filter_by(is_active=True).count(),
            'total_companies': Company.query.filter_by(is_active=True).count(),
            'total_people': Person.query.filter_by(is_active=True).count(),
            'categories_usage': []
        }
        
        categories = CapitalCategory.query.filter_by(is_active=True).all()
        for category in categories:
            companies_count = category.current_companies_count
            
            stats['categories_usage'].append({
                'name': category.name,
                'current': companies_count,
                'max': category.max_companies,
                'percentage': category.usage_percentage
            })
        
        return jsonify(stats)
    
    except Exception as e:
        current_app.logger.error(f"Error in stats API: {str(e)}")
        return jsonify({'error': 'حدث خطأ في تحميل الإحصائيات'})

@main_bp.route('/health')
def health_check():
    """فحص صحة التطبيق"""
    try:
        # فحص قاعدة البيانات
        db.session.execute('SELECT 1')
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '2.0.0'
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

@main_bp.errorhandler(403)
def forbidden(error):
    """معالج خطأ 403"""
    return render_template('errors/403.html'), 403

@main_bp.errorhandler(404)
def not_found(error):
    """معالج خطأ 404"""
    return render_template('errors/404.html'), 404

@main_bp.errorhandler(500)
def internal_error(error):
    """معالج خطأ 500"""
    db.session.rollback()
    current_app.logger.error(f"Internal error: {str(error)}")
    return render_template('errors/500.html'), 500

# إضافة دوال مساعدة للقوالب
@main_bp.app_template_global()
def has_permission(permission):
    """دالة للتحقق من الصلاحيات في القوالب"""
    from permissions import PermissionManager
    return PermissionManager.has_permission(current_user, permission)

@main_bp.app_template_global()
def can_access_resource(resource_type, action):
    """دالة للتحقق من الوصول للموارد في القوالب"""
    from permissions import PermissionManager
    return PermissionManager.can_access_resource(current_user, resource_type, action)

@main_bp.app_template_filter('arabic_date')
def arabic_date_filter(date_obj):
    """فلتر لتنسيق التاريخ بالعربية"""
    if not date_obj:
        return ""
    
    from utils import format_arabic_date
    return format_arabic_date(date_obj, include_time=True)

@main_bp.app_template_filter('file_size')
def file_size_filter(size_bytes):
    """فلتر لتنسيق حجم الملف"""
    if not size_bytes:
        return "0 B"
    
    from utils import format_file_size
    return format_file_size(size_bytes)
