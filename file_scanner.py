#!/usr/bin/env python3
"""
نظام فحص الملفات والحماية من الملفات الضارة
File Scanner and Malware Protection System
"""

import os
import hashlib
import mimetypes
import magic
from pathlib import Path
import zipfile
import tarfile
from PIL import Image
import subprocess
import tempfile

class FileScanner:
    """فئة فحص الملفات"""
    
    def __init__(self):
        self.dangerous_signatures = [
            # PE executable signatures
            b'\x4D\x5A',  # MZ header (Windows executable)
            b'\x50\x4B\x03\x04',  # ZIP file (could contain executables)
            
            # Script signatures
            b'#!/bin/bash',
            b'#!/bin/sh',
            b'@echo off',
            b'<script',
            b'<?php',
            b'<%',
            
            # Suspicious patterns
            b'eval(',
            b'exec(',
            b'system(',
            b'shell_exec(',
            b'passthru(',
            b'base64_decode(',
        ]
        
        self.suspicious_extensions = {
            'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 
            'jar', 'php', 'py', 'sh', 'ps1', 'msi', 'deb', 'rpm'
        }
        
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.max_archive_files = 1000  # Maximum files in archive
    
    def scan_file(self, file_path):
        """فحص شامل للملف"""
        results = {
            'safe': True,
            'threats': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                results['safe'] = False
                results['threats'].append('الملف غير موجود')
                return results
            
            # معلومات الملف الأساسية
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            
            results['file_info'] = {
                'size': file_size,
                'path': file_path,
                'extension': Path(file_path).suffix.lower(),
                'mime_type': self._get_mime_type(file_path)
            }
            
            # فحص حجم الملف
            if file_size > self.max_file_size:
                results['safe'] = False
                results['threats'].append(f'حجم الملف كبير جداً ({file_size} bytes)')
                return results
            
            # فحص الامتداد
            extension = Path(file_path).suffix.lower().lstrip('.')
            if extension in self.suspicious_extensions:
                results['safe'] = False
                results['threats'].append(f'امتداد ملف خطير: .{extension}')
                return results
            
            # فحص محتوى الملف
            content_scan = self._scan_file_content(file_path)
            if not content_scan['safe']:
                results['safe'] = False
                results['threats'].extend(content_scan['threats'])
            
            # فحص الصور
            if self._is_image_file(file_path):
                image_scan = self._scan_image_file(file_path)
                if not image_scan['safe']:
                    results['safe'] = False
                    results['threats'].extend(image_scan['threats'])
                results['warnings'].extend(image_scan.get('warnings', []))
            
            # فحص الأرشيف
            if self._is_archive_file(file_path):
                archive_scan = self._scan_archive_file(file_path)
                if not archive_scan['safe']:
                    results['safe'] = False
                    results['threats'].extend(archive_scan['threats'])
                results['warnings'].extend(archive_scan.get('warnings', []))
            
            # فحص البيانات الوصفية
            metadata_scan = self._scan_metadata(file_path)
            results['warnings'].extend(metadata_scan.get('warnings', []))
            
        except Exception as e:
            results['safe'] = False
            results['threats'].append(f'خطأ في فحص الملف: {str(e)}')
        
        return results
    
    def _get_mime_type(self, file_path):
        """الحصول على نوع MIME للملف"""
        try:
            # استخدام python-magic إذا كان متاحاً
            if hasattr(magic, 'from_file'):
                return magic.from_file(file_path, mime=True)
            else:
                # استخدام mimetypes كبديل
                mime_type, _ = mimetypes.guess_type(file_path)
                return mime_type or 'application/octet-stream'
        except:
            return 'unknown'
    
    def _scan_file_content(self, file_path):
        """فحص محتوى الملف للبحث عن أنماط خطيرة"""
        results = {'safe': True, 'threats': []}
        
        try:
            with open(file_path, 'rb') as f:
                # قراءة أول 8KB من الملف
                content = f.read(8192)
                
                # البحث عن التوقيعات الخطيرة
                for signature in self.dangerous_signatures:
                    if signature in content:
                        results['safe'] = False
                        results['threats'].append(f'تم العثور على نمط خطير في الملف')
                        break
                
                # فحص إضافي للنصوص
                try:
                    text_content = content.decode('utf-8', errors='ignore').lower()
                    
                    # كلمات مفتاحية خطيرة
                    dangerous_keywords = [
                        'malware', 'virus', 'trojan', 'backdoor', 'keylogger',
                        'ransomware', 'rootkit', 'exploit', 'payload'
                    ]
                    
                    for keyword in dangerous_keywords:
                        if keyword in text_content:
                            results['threats'].append(f'تم العثور على كلمة مفتاحية مشبوهة: {keyword}')
                            
                except UnicodeDecodeError:
                    pass  # ملف ثنائي، لا يمكن فحصه كنص
                    
        except Exception as e:
            results['threats'].append(f'خطأ في فحص المحتوى: {str(e)}')
        
        return results
    
    def _is_image_file(self, file_path):
        """التحقق من أن الملف صورة"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}
        return Path(file_path).suffix.lower() in image_extensions
    
    def _scan_image_file(self, file_path):
        """فحص ملف الصورة"""
        results = {'safe': True, 'threats': [], 'warnings': []}
        
        try:
            with Image.open(file_path) as img:
                # التحقق من أبعاد الصورة
                width, height = img.size
                if width > 10000 or height > 10000:
                    results['warnings'].append('أبعاد الصورة كبيرة جداً')
                
                # التحقق من عدد الإطارات (للصور المتحركة)
                if hasattr(img, 'n_frames') and img.n_frames > 100:
                    results['warnings'].append('عدد إطارات كبير في الصورة المتحركة')
                
                # فحص البيانات الوصفية
                if hasattr(img, '_getexif') and img._getexif():
                    exif_data = img._getexif()
                    if exif_data and len(str(exif_data)) > 10000:
                        results['warnings'].append('بيانات EXIF كبيرة')
                        
        except Exception as e:
            results['safe'] = False
            results['threats'].append(f'ملف صورة تالف أو خطير: {str(e)}')
        
        return results
    
    def _is_archive_file(self, file_path):
        """التحقق من أن الملف أرشيف"""
        archive_extensions = {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'}
        return Path(file_path).suffix.lower() in archive_extensions
    
    def _scan_archive_file(self, file_path):
        """فحص ملف الأرشيف"""
        results = {'safe': True, 'threats': [], 'warnings': []}
        
        try:
            if file_path.lower().endswith('.zip'):
                with zipfile.ZipFile(file_path, 'r') as archive:
                    file_list = archive.namelist()
                    
                    # التحقق من عدد الملفات
                    if len(file_list) > self.max_archive_files:
                        results['safe'] = False
                        results['threats'].append('عدد ملفات كبير في الأرشيف')
                        return results
                    
                    # فحص أسماء الملفات
                    for filename in file_list:
                        if self._is_suspicious_filename(filename):
                            results['safe'] = False
                            results['threats'].append(f'اسم ملف مشبوه في الأرشيف: {filename}')
                            
            elif file_path.lower().endswith(('.tar', '.tar.gz', '.tar.bz2')):
                with tarfile.open(file_path, 'r') as archive:
                    file_list = archive.getnames()
                    
                    if len(file_list) > self.max_archive_files:
                        results['safe'] = False
                        results['threats'].append('عدد ملفات كبير في الأرشيف')
                        return results
                    
                    for filename in file_list:
                        if self._is_suspicious_filename(filename):
                            results['safe'] = False
                            results['threats'].append(f'اسم ملف مشبوه في الأرشيف: {filename}')
                            
        except Exception as e:
            results['safe'] = False
            results['threats'].append(f'خطأ في فحص الأرشيف: {str(e)}')
        
        return results
    
    def _is_suspicious_filename(self, filename):
        """التحقق من أن اسم الملف مشبوه"""
        suspicious_patterns = [
            '..', '/', '\\', '<', '>', '|', '?', '*', ':', '"',
            'autorun.inf', 'desktop.ini', '.htaccess'
        ]
        
        filename_lower = filename.lower()
        
        # فحص الأنماط المشبوهة
        for pattern in suspicious_patterns:
            if pattern in filename_lower:
                return True
        
        # فحص الامتدادات الخطيرة
        extension = Path(filename).suffix.lower().lstrip('.')
        if extension in self.suspicious_extensions:
            return True
        
        return False
    
    def _scan_metadata(self, file_path):
        """فحص البيانات الوصفية"""
        results = {'warnings': []}
        
        try:
            # فحص حجم البيانات الوصفية
            file_stat = os.stat(file_path)
            if file_stat.st_size > 0:
                # يمكن إضافة فحوصات أخرى للبيانات الوصفية هنا
                pass
                
        except Exception:
            pass
        
        return results
    
    def quarantine_file(self, file_path, reason="ملف خطير"):
        """عزل الملف الخطير"""
        try:
            quarantine_dir = Path('quarantine')
            quarantine_dir.mkdir(exist_ok=True)
            
            # إنشاء اسم فريد للملف المعزول
            import uuid
            quarantine_filename = f"{uuid.uuid4().hex}_{Path(file_path).name}"
            quarantine_path = quarantine_dir / quarantine_filename
            
            # نقل الملف للحجر الصحي
            os.rename(file_path, quarantine_path)
            
            # تسجيل العملية
            log_entry = f"{datetime.now().isoformat()}: {file_path} -> {quarantine_path} ({reason})\n"
            with open(quarantine_dir / 'quarantine.log', 'a', encoding='utf-8') as log_file:
                log_file.write(log_entry)
            
            return True, str(quarantine_path)
            
        except Exception as e:
            return False, str(e)

# إنشاء مثيل عام من فاحص الملفات
file_scanner = FileScanner()
