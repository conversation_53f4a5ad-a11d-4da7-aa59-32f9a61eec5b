#!/usr/bin/env python3
"""
نظام الأدوار والصلاحيات
Roles and Permissions System
"""

from enum import Enum
from functools import wraps
from flask import abort, current_app
from flask_login import current_user

class Permission(Enum):
    """تعداد الصلاحيات المختلفة"""
    # صلاحيات المستخدمين
    VIEW_USERS = "view_users"
    CREATE_USER = "create_user"
    EDIT_USER = "edit_user"
    DELETE_USER = "delete_user"
    
    # صلاحيات الشركات
    VIEW_COMPANIES = "view_companies"
    CREATE_COMPANY = "create_company"
    EDIT_COMPANY = "edit_company"
    DELETE_COMPANY = "delete_company"
    
    # صلاحيات الأشخاص
    VIEW_PEOPLE = "view_people"
    CREATE_PERSON = "create_person"
    EDIT_PERSON = "edit_person"
    DELETE_PERSON = "delete_person"
    
    # صلاحيات فئات رأس المال
    VIEW_CATEGORIES = "view_categories"
    CREATE_CATEGORY = "create_category"
    EDIT_CATEGORY = "edit_category"
    DELETE_CATEGORY = "delete_category"
    
    # صلاحيات الملفات
    VIEW_FILES = "view_files"
    UPLOAD_FILES = "upload_files"
    DELETE_FILES = "delete_files"
    
    # صلاحيات النظام
    VIEW_SYSTEM_INFO = "view_system_info"
    MANAGE_BACKUPS = "manage_backups"
    VIEW_AUDIT_LOGS = "view_audit_logs"
    SYSTEM_ADMIN = "system_admin"

class Role(Enum):
    """تعداد الأدوار المختلفة"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MANAGER = "manager"
    EMPLOYEE = "employee"
    VIEWER = "viewer"

# تعريف الصلاحيات لكل دور
ROLE_PERMISSIONS = {
    Role.SUPER_ADMIN: [
        # جميع الصلاحيات
        Permission.VIEW_USERS, Permission.CREATE_USER, Permission.EDIT_USER, Permission.DELETE_USER,
        Permission.VIEW_COMPANIES, Permission.CREATE_COMPANY, Permission.EDIT_COMPANY, Permission.DELETE_COMPANY,
        Permission.VIEW_PEOPLE, Permission.CREATE_PERSON, Permission.EDIT_PERSON, Permission.DELETE_PERSON,
        Permission.VIEW_CATEGORIES, Permission.CREATE_CATEGORY, Permission.EDIT_CATEGORY, Permission.DELETE_CATEGORY,
        Permission.VIEW_FILES, Permission.UPLOAD_FILES, Permission.DELETE_FILES,
        Permission.VIEW_SYSTEM_INFO, Permission.MANAGE_BACKUPS, Permission.VIEW_AUDIT_LOGS, Permission.SYSTEM_ADMIN,
    ],
    
    Role.ADMIN: [
        # معظم الصلاحيات عدا إدارة المستخدمين والنظام
        Permission.VIEW_COMPANIES, Permission.CREATE_COMPANY, Permission.EDIT_COMPANY, Permission.DELETE_COMPANY,
        Permission.VIEW_PEOPLE, Permission.CREATE_PERSON, Permission.EDIT_PERSON, Permission.DELETE_PERSON,
        Permission.VIEW_CATEGORIES, Permission.CREATE_CATEGORY, Permission.EDIT_CATEGORY, Permission.DELETE_CATEGORY,
        Permission.VIEW_FILES, Permission.UPLOAD_FILES, Permission.DELETE_FILES,
        Permission.VIEW_SYSTEM_INFO, Permission.MANAGE_BACKUPS,
    ],
    
    Role.MANAGER: [
        # إدارة الشركات والأشخاص
        Permission.VIEW_COMPANIES, Permission.CREATE_COMPANY, Permission.EDIT_COMPANY,
        Permission.VIEW_PEOPLE, Permission.CREATE_PERSON, Permission.EDIT_PERSON,
        Permission.VIEW_CATEGORIES,
        Permission.VIEW_FILES, Permission.UPLOAD_FILES,
    ],
    
    Role.EMPLOYEE: [
        # عرض وإنشاء فقط
        Permission.VIEW_COMPANIES, Permission.CREATE_COMPANY,
        Permission.VIEW_PEOPLE, Permission.CREATE_PERSON,
        Permission.VIEW_CATEGORIES,
        Permission.VIEW_FILES, Permission.UPLOAD_FILES,
    ],
    
    Role.VIEWER: [
        # عرض فقط
        Permission.VIEW_COMPANIES,
        Permission.VIEW_PEOPLE,
        Permission.VIEW_CATEGORIES,
        Permission.VIEW_FILES,
    ],
}

class PermissionManager:
    """مدير الصلاحيات"""
    
    @staticmethod
    def get_user_role(user):
        """الحصول على دور المستخدم"""
        if not user or not user.is_authenticated:
            return None
        
        # للتوافق مع النظام الحالي
        if hasattr(user, 'role'):
            return Role(user.role)
        elif user.is_admin:
            return Role.SUPER_ADMIN
        else:
            return Role.EMPLOYEE
    
    @staticmethod
    def has_permission(user, permission):
        """التحقق من وجود صلاحية للمستخدم"""
        if not user or not user.is_authenticated:
            return False
        
        user_role = PermissionManager.get_user_role(user)
        if not user_role:
            return False
        
        user_permissions = ROLE_PERMISSIONS.get(user_role, [])
        return permission in user_permissions
    
    @staticmethod
    def require_permission(permission):
        """ديكوريتر للتحقق من الصلاحية"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                if not PermissionManager.has_permission(current_user, permission):
                    abort(403, f'ليس لديك صلاحية: {permission.value}')
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    @staticmethod
    def require_any_permission(*permissions):
        """ديكوريتر للتحقق من وجود أي من الصلاحيات"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                has_any = any(
                    PermissionManager.has_permission(current_user, perm) 
                    for perm in permissions
                )
                if not has_any:
                    abort(403, 'ليس لديك الصلاحيات المطلوبة')
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    @staticmethod
    def get_user_permissions(user):
        """الحصول على جميع صلاحيات المستخدم"""
        user_role = PermissionManager.get_user_role(user)
        if not user_role:
            return []
        
        return ROLE_PERMISSIONS.get(user_role, [])
    
    @staticmethod
    def can_access_resource(user, resource_type, action):
        """التحقق من إمكانية الوصول لمورد معين"""
        permission_map = {
            ('company', 'view'): Permission.VIEW_COMPANIES,
            ('company', 'create'): Permission.CREATE_COMPANY,
            ('company', 'edit'): Permission.EDIT_COMPANY,
            ('company', 'delete'): Permission.DELETE_COMPANY,
            
            ('person', 'view'): Permission.VIEW_PEOPLE,
            ('person', 'create'): Permission.CREATE_PERSON,
            ('person', 'edit'): Permission.EDIT_PERSON,
            ('person', 'delete'): Permission.DELETE_PERSON,
            
            ('category', 'view'): Permission.VIEW_CATEGORIES,
            ('category', 'create'): Permission.CREATE_CATEGORY,
            ('category', 'edit'): Permission.EDIT_CATEGORY,
            ('category', 'delete'): Permission.DELETE_CATEGORY,
            
            ('file', 'view'): Permission.VIEW_FILES,
            ('file', 'upload'): Permission.UPLOAD_FILES,
            ('file', 'delete'): Permission.DELETE_FILES,
        }
        
        required_permission = permission_map.get((resource_type, action))
        if not required_permission:
            return False
        
        return PermissionManager.has_permission(user, required_permission)

# إنشاء مثيل عام من مدير الصلاحيات
permission_manager = PermissionManager()

# ديكوريتر مختصر للاستخدام السريع
def require_permission(permission):
    """ديكوريتر مختصر للتحقق من الصلاحية"""
    return PermissionManager.require_permission(permission)

def require_any_permission(*permissions):
    """ديكوريتر مختصر للتحقق من أي صلاحية"""
    return PermissionManager.require_any_permission(*permissions)

# دوال مساعدة للقوالب
def has_permission(permission):
    """دالة للاستخدام في القوالب"""
    return PermissionManager.has_permission(current_user, permission)

def can_access_resource(resource_type, action):
    """دالة للاستخدام في القوالب"""
    return PermissionManager.can_access_resource(current_user, resource_type, action)
