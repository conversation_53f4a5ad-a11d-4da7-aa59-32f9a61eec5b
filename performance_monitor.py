#!/usr/bin/env python3
"""
نظام مراقبة الأداء
Performance Monitoring System
"""

import time
import psutil
import threading
from datetime import datetime, timedelta
from collections import deque, defaultdict
import json
from pathlib import Path
from flask import current_app
import sqlite3

class PerformanceMonitor:
    """مراقب الأداء"""
    
    def __init__(self):
        self.metrics_history = deque(maxlen=1000)  # آخر 1000 قياس
        self.request_times = deque(maxlen=10000)  # آخر 10000 طلب
        self.error_counts = defaultdict(int)
        self.endpoint_stats = defaultdict(lambda: {'count': 0, 'total_time': 0, 'errors': 0})
        
        self.monitoring = False
        self.monitor_thread = None
        
        # إعداد قاعدة بيانات المقاييس
        self.db_path = Path('logs/metrics.db')
        self.db_path.parent.mkdir(exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """تهيئة قاعدة بيانات المقاييس"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS system_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        cpu_percent REAL,
                        memory_percent REAL,
                        memory_used INTEGER,
                        disk_percent REAL,
                        disk_used INTEGER,
                        active_connections INTEGER
                    )
                ''')
                
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS request_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        endpoint TEXT,
                        method TEXT,
                        response_time REAL,
                        status_code INTEGER,
                        user_id INTEGER
                    )
                ''')
                
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS error_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        error_type TEXT,
                        error_message TEXT,
                        endpoint TEXT,
                        user_id INTEGER,
                        stack_trace TEXT
                    )
                ''')
                
                # إنشاء فهارس للأداء
                conn.execute('CREATE INDEX IF NOT EXISTS idx_system_timestamp ON system_metrics(timestamp)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_request_timestamp ON request_metrics(timestamp)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_error_timestamp ON error_logs(timestamp)')
                
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة بيانات المقاييس: {e}")
    
    def start_monitoring(self, interval=60):
        """بدء مراقبة النظام"""
        if self.monitoring:
            return False
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        return True
    
    def stop_monitoring(self):
        """إيقاف مراقبة النظام"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        return True
    
    def _monitoring_loop(self, interval):
        """حلقة مراقبة النظام"""
        while self.monitoring:
            try:
                metrics = self._collect_system_metrics()
                self._store_system_metrics(metrics)
                self.metrics_history.append(metrics)
                
                # تنظيف البيانات القديمة كل ساعة
                if len(self.metrics_history) % 60 == 0:
                    self._cleanup_old_data()
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"خطأ في حلقة المراقبة: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """جمع مقاييس النظام"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # عدد الاتصالات النشطة (تقدير)
            active_connections = len(psutil.net_connections())
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used': memory.used,
                'memory_total': memory.total,
                'disk_percent': disk.percent,
                'disk_used': disk.used,
                'disk_total': disk.total,
                'active_connections': active_connections
            }
            
        except Exception as e:
            print(f"خطأ في جمع مقاييس النظام: {e}")
            return {}
    
    def _store_system_metrics(self, metrics):
        """حفظ مقاييس النظام في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO system_metrics 
                    (timestamp, cpu_percent, memory_percent, memory_used, 
                     disk_percent, disk_used, active_connections)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    metrics['timestamp'],
                    metrics.get('cpu_percent'),
                    metrics.get('memory_percent'),
                    metrics.get('memory_used'),
                    metrics.get('disk_percent'),
                    metrics.get('disk_used'),
                    metrics.get('active_connections')
                ))
                
        except Exception as e:
            print(f"خطأ في حفظ مقاييس النظام: {e}")
    
    def record_request(self, endpoint, method, response_time, status_code, user_id=None):
        """تسجيل طلب HTTP"""
        try:
            # إضافة للذاكرة
            request_data = {
                'timestamp': datetime.now().isoformat(),
                'endpoint': endpoint,
                'method': method,
                'response_time': response_time,
                'status_code': status_code,
                'user_id': user_id
            }
            
            self.request_times.append(request_data)
            
            # تحديث إحصائيات النقاط النهائية
            endpoint_key = f"{method} {endpoint}"
            self.endpoint_stats[endpoint_key]['count'] += 1
            self.endpoint_stats[endpoint_key]['total_time'] += response_time
            
            if status_code >= 400:
                self.endpoint_stats[endpoint_key]['errors'] += 1
                self.error_counts[status_code] += 1
            
            # حفظ في قاعدة البيانات
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO request_metrics 
                    (timestamp, endpoint, method, response_time, status_code, user_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    request_data['timestamp'],
                    endpoint,
                    method,
                    response_time,
                    status_code,
                    user_id
                ))
                
        except Exception as e:
            print(f"خطأ في تسجيل الطلب: {e}")
    
    def record_error(self, error_type, error_message, endpoint=None, user_id=None, stack_trace=None):
        """تسجيل خطأ"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO error_logs 
                    (timestamp, error_type, error_message, endpoint, user_id, stack_trace)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    datetime.now().isoformat(),
                    error_type,
                    error_message,
                    endpoint,
                    user_id,
                    stack_trace
                ))
                
        except Exception as e:
            print(f"خطأ في تسجيل الخطأ: {e}")
    
    def get_current_metrics(self):
        """الحصول على المقاييس الحالية"""
        if not self.metrics_history:
            return self._collect_system_metrics()
        
        return self.metrics_history[-1]
    
    def get_performance_summary(self, hours=24):
        """الحصول على ملخص الأداء"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_str = cutoff_time.isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                # إحصائيات الطلبات
                cursor = conn.execute('''
                    SELECT 
                        COUNT(*) as total_requests,
                        AVG(response_time) as avg_response_time,
                        MAX(response_time) as max_response_time,
                        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_count
                    FROM request_metrics 
                    WHERE timestamp > ?
                ''', (cutoff_str,))
                
                request_stats = cursor.fetchone()
                
                # إحصائيات النظام
                cursor = conn.execute('''
                    SELECT 
                        AVG(cpu_percent) as avg_cpu,
                        MAX(cpu_percent) as max_cpu,
                        AVG(memory_percent) as avg_memory,
                        MAX(memory_percent) as max_memory
                    FROM system_metrics 
                    WHERE timestamp > ?
                ''', (cutoff_str,))
                
                system_stats = cursor.fetchone()
                
                # أبطأ النقاط النهائية
                cursor = conn.execute('''
                    SELECT endpoint, method, AVG(response_time) as avg_time, COUNT(*) as count
                    FROM request_metrics 
                    WHERE timestamp > ?
                    GROUP BY endpoint, method
                    ORDER BY avg_time DESC
                    LIMIT 10
                ''', (cutoff_str,))
                
                slow_endpoints = cursor.fetchall()
                
                return {
                    'period_hours': hours,
                    'request_stats': {
                        'total_requests': request_stats[0] or 0,
                        'avg_response_time': round(request_stats[1] or 0, 2),
                        'max_response_time': round(request_stats[2] or 0, 2),
                        'error_count': request_stats[3] or 0,
                        'error_rate': round((request_stats[3] or 0) / max(request_stats[0] or 1, 1) * 100, 2)
                    },
                    'system_stats': {
                        'avg_cpu': round(system_stats[0] or 0, 1),
                        'max_cpu': round(system_stats[1] or 0, 1),
                        'avg_memory': round(system_stats[2] or 0, 1),
                        'max_memory': round(system_stats[3] or 0, 1)
                    },
                    'slow_endpoints': [
                        {
                            'endpoint': row[0],
                            'method': row[1],
                            'avg_time': round(row[2], 2),
                            'count': row[3]
                        }
                        for row in slow_endpoints
                    ]
                }
                
        except Exception as e:
            print(f"خطأ في الحصول على ملخص الأداء: {e}")
            return {}
    
    def get_real_time_stats(self):
        """الحصول على إحصائيات الوقت الفعلي"""
        try:
            # آخر 100 طلب
            recent_requests = list(self.request_times)[-100:]
            
            if not recent_requests:
                return {}
            
            # حساب الإحصائيات
            response_times = [req['response_time'] for req in recent_requests]
            status_codes = [req['status_code'] for req in recent_requests]
            
            avg_response_time = sum(response_times) / len(response_times)
            error_count = sum(1 for code in status_codes if code >= 400)
            
            return {
                'recent_requests_count': len(recent_requests),
                'avg_response_time': round(avg_response_time, 2),
                'error_count': error_count,
                'error_rate': round(error_count / len(recent_requests) * 100, 2),
                'current_system': self.get_current_metrics()
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات الوقت الفعلي: {e}")
            return {}
    
    def _cleanup_old_data(self, days=30):
        """تنظيف البيانات القديمة"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            cutoff_str = cutoff_time.isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                # حذف المقاييس القديمة
                conn.execute('DELETE FROM system_metrics WHERE timestamp < ?', (cutoff_str,))
                conn.execute('DELETE FROM request_metrics WHERE timestamp < ?', (cutoff_str,))
                conn.execute('DELETE FROM error_logs WHERE timestamp < ?', (cutoff_str,))
                
                # ضغط قاعدة البيانات
                conn.execute('VACUUM')
                
        except Exception as e:
            print(f"خطأ في تنظيف البيانات القديمة: {e}")
    
    def get_health_status(self):
        """الحصول على حالة صحة النظام"""
        try:
            current_metrics = self.get_current_metrics()
            real_time_stats = self.get_real_time_stats()
            
            # تحديد حالة الصحة
            health_score = 100
            issues = []
            
            # فحص استخدام المعالج
            cpu_percent = current_metrics.get('cpu_percent', 0)
            if cpu_percent > 90:
                health_score -= 30
                issues.append('استخدام المعالج مرتفع جداً')
            elif cpu_percent > 70:
                health_score -= 15
                issues.append('استخدام المعالج مرتفع')
            
            # فحص استخدام الذاكرة
            memory_percent = current_metrics.get('memory_percent', 0)
            if memory_percent > 90:
                health_score -= 25
                issues.append('استخدام الذاكرة مرتفع جداً')
            elif memory_percent > 80:
                health_score -= 10
                issues.append('استخدام الذاكرة مرتفع')
            
            # فحص معدل الأخطاء
            error_rate = real_time_stats.get('error_rate', 0)
            if error_rate > 10:
                health_score -= 20
                issues.append('معدل أخطاء مرتفع')
            elif error_rate > 5:
                health_score -= 10
                issues.append('معدل أخطاء متوسط')
            
            # فحص وقت الاستجابة
            avg_response_time = real_time_stats.get('avg_response_time', 0)
            if avg_response_time > 2000:  # أكثر من ثانيتين
                health_score -= 15
                issues.append('وقت الاستجابة بطيء')
            elif avg_response_time > 1000:  # أكثر من ثانية
                health_score -= 5
                issues.append('وقت الاستجابة متوسط')
            
            # تحديد الحالة العامة
            if health_score >= 90:
                status = 'excellent'
                status_text = 'ممتاز'
            elif health_score >= 70:
                status = 'good'
                status_text = 'جيد'
            elif health_score >= 50:
                status = 'warning'
                status_text = 'تحذير'
            else:
                status = 'critical'
                status_text = 'حرج'
            
            return {
                'status': status,
                'status_text': status_text,
                'health_score': max(0, health_score),
                'issues': issues,
                'metrics': current_metrics,
                'stats': real_time_stats
            }
            
        except Exception as e:
            print(f"خطأ في فحص حالة الصحة: {e}")
            return {
                'status': 'unknown',
                'status_text': 'غير معروف',
                'health_score': 0,
                'issues': ['خطأ في فحص الحالة'],
                'metrics': {},
                'stats': {}
            }

# إنشاء مثيل عام من مراقب الأداء
performance_monitor = PerformanceMonitor()
