#!/usr/bin/env python3
"""
نظام التسجيل والمراقبة المتقدم
Advanced Logging and Monitoring System
"""

import logging
import logging.handlers
import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from functools import wraps
from flask import request, current_app, g
from flask_login import current_user
import threading
import queue
import psutil

class AdvancedLogger:
    """نظام التسجيل المتقدم"""
    
    def __init__(self, app=None):
        self.app = app
        self.log_queue = queue.Queue()
        self.log_worker = None
        self.running = False
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة النظام مع التطبيق"""
        self.app = app
        
        # إنشاء مجلد السجلات
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        # إعداد السجلات المختلفة
        self._setup_application_logger(log_dir)
        self._setup_security_logger(log_dir)
        self._setup_performance_logger(log_dir)
        self._setup_audit_logger(log_dir)
        
        # بدء معالج السجلات
        self.start_log_worker()
        
        # إضافة معالجات الطلبات
        app.before_request(self._before_request)
        app.after_request(self._after_request)
        app.teardown_appcontext(self._teardown_request)
    
    def _setup_application_logger(self, log_dir):
        """إعداد سجل التطبيق العام"""
        app_logger = logging.getLogger('app')
        app_logger.setLevel(logging.INFO)
        
        # معالج الملف مع التدوير
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'app.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        
        # تنسيق السجل
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        app_logger.addHandler(file_handler)
        
        self.app_logger = app_logger
    
    def _setup_security_logger(self, log_dir):
        """إعداد سجل الأمان"""
        security_logger = logging.getLogger('security')
        security_logger.setLevel(logging.WARNING)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'security.log',
            maxBytes=5*1024*1024,  # 5MB
            backupCount=20,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        security_logger.addHandler(file_handler)
        
        self.security_logger = security_logger
    
    def _setup_performance_logger(self, log_dir):
        """إعداد سجل الأداء"""
        perf_logger = logging.getLogger('performance')
        perf_logger.setLevel(logging.INFO)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'performance.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - PERF - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        perf_logger.addHandler(file_handler)
        
        self.perf_logger = perf_logger
    
    def _setup_audit_logger(self, log_dir):
        """إعداد سجل المراجعة"""
        audit_logger = logging.getLogger('audit')
        audit_logger.setLevel(logging.INFO)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'audit.log',
            maxBytes=20*1024*1024,  # 20MB
            backupCount=50,
            encoding='utf-8'
        )
        
        # تنسيق JSON للمراجعة
        formatter = logging.Formatter('%(message)s')
        file_handler.setFormatter(formatter)
        audit_logger.addHandler(file_handler)
        
        self.audit_logger = audit_logger
    
    def _before_request(self):
        """معالج ما قبل الطلب"""
        g.start_time = time.time()
        g.request_id = self._generate_request_id()
        
        # تسجيل بداية الطلب
        self.log_request_start()
    
    def _after_request(self, response):
        """معالج ما بعد الطلب"""
        # حساب وقت الاستجابة
        if hasattr(g, 'start_time'):
            response_time = time.time() - g.start_time
            g.response_time = response_time
            
            # تسجيل انتهاء الطلب
            self.log_request_end(response)
            
            # تسجيل الطلبات البطيئة
            if response_time > 2.0:  # أكثر من ثانيتين
                self.log_slow_request(response_time)
        
        return response
    
    def _teardown_request(self, exception):
        """معالج تنظيف الطلب"""
        if exception:
            self.log_request_exception(exception)
    
    def _generate_request_id(self):
        """إنشاء معرف فريد للطلب"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def log_request_start(self):
        """تسجيل بداية الطلب"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'request_id': g.request_id,
                'method': request.method,
                'url': request.url,
                'endpoint': request.endpoint,
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'user_id': current_user.id if current_user.is_authenticated else None,
                'username': current_user.username if current_user.is_authenticated else None
            }
            
            self.app_logger.info(f"REQUEST_START: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            self.app_logger.error(f"خطأ في تسجيل بداية الطلب: {e}")
    
    def log_request_end(self, response):
        """تسجيل انتهاء الطلب"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'request_id': g.request_id,
                'status_code': response.status_code,
                'response_time': round(g.response_time * 1000, 2),  # بالميلي ثانية
                'content_length': response.content_length
            }
            
            self.app_logger.info(f"REQUEST_END: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            self.app_logger.error(f"خطأ في تسجيل انتهاء الطلب: {e}")
    
    def log_slow_request(self, response_time):
        """تسجيل الطلبات البطيئة"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'request_id': g.request_id,
                'method': request.method,
                'url': request.url,
                'response_time': round(response_time * 1000, 2),
                'user_id': current_user.id if current_user.is_authenticated else None
            }
            
            self.perf_logger.warning(f"SLOW_REQUEST: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            self.app_logger.error(f"خطأ في تسجيل الطلب البطيء: {e}")
    
    def log_request_exception(self, exception):
        """تسجيل استثناءات الطلبات"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'request_id': g.request_id,
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'method': request.method,
                'url': request.url,
                'user_id': current_user.id if current_user.is_authenticated else None
            }
            
            self.app_logger.error(f"REQUEST_EXCEPTION: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            self.app_logger.error(f"خطأ في تسجيل استثناء الطلب: {e}")
    
    def log_security_event(self, event_type, details, severity='WARNING'):
        """تسجيل الأحداث الأمنية"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'details': details,
                'severity': severity,
                'remote_addr': request.remote_addr if request else None,
                'user_agent': request.headers.get('User-Agent', '') if request else '',
                'user_id': current_user.id if current_user.is_authenticated else None,
                'username': current_user.username if current_user.is_authenticated else None
            }
            
            if severity == 'CRITICAL':
                self.security_logger.critical(json.dumps(log_data, ensure_ascii=False))
            elif severity == 'ERROR':
                self.security_logger.error(json.dumps(log_data, ensure_ascii=False))
            else:
                self.security_logger.warning(json.dumps(log_data, ensure_ascii=False))
                
        except Exception as e:
            self.app_logger.error(f"خطأ في تسجيل الحدث الأمني: {e}")
    
    def log_audit_event(self, action, resource_type, resource_id=None, old_values=None, new_values=None):
        """تسجيل أحداث المراجعة"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'resource_type': resource_type,
                'resource_id': resource_id,
                'old_values': old_values,
                'new_values': new_values,
                'user_id': current_user.id if current_user.is_authenticated else None,
                'username': current_user.username if current_user.is_authenticated else None,
                'remote_addr': request.remote_addr if request else None,
                'user_agent': request.headers.get('User-Agent', '') if request else ''
            }
            
            self.audit_logger.info(json.dumps(log_data, ensure_ascii=False))
            
        except Exception as e:
            self.app_logger.error(f"خطأ في تسجيل حدث المراجعة: {e}")
    
    def start_log_worker(self):
        """بدء معالج السجلات في خيط منفصل"""
        if not self.running:
            self.running = True
            self.log_worker = threading.Thread(target=self._log_worker_loop, daemon=True)
            self.log_worker.start()
    
    def _log_worker_loop(self):
        """حلقة معالج السجلات"""
        while self.running:
            try:
                # معالجة السجلات من القائمة
                log_entry = self.log_queue.get(timeout=1)
                self._process_log_entry(log_entry)
                self.log_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"خطأ في معالج السجلات: {e}")
    
    def _process_log_entry(self, log_entry):
        """معالجة إدخال السجل"""
        # يمكن إضافة معالجة إضافية هنا
        # مثل إرسال التنبيهات أو حفظ في قاعدة بيانات
        pass
    
    def get_system_metrics(self):
        """الحصول على مقاييس النظام"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used': memory.used,
                'memory_total': memory.total,
                'disk_percent': disk.percent,
                'disk_used': disk.used,
                'disk_total': disk.total
            }
            
            self.perf_logger.info(f"SYSTEM_METRICS: {json.dumps(metrics)}")
            return metrics
            
        except Exception as e:
            self.app_logger.error(f"خطأ في جمع مقاييس النظام: {e}")
            return {}

def log_activity(action, resource_type, resource_id=None):
    """ديكوريتر لتسجيل النشاط"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # تنفيذ الدالة
                result = f(*args, **kwargs)
                
                # تسجيل النشاط الناجح
                if hasattr(current_app, 'logger_system'):
                    current_app.logger_system.log_audit_event(
                        action=action,
                        resource_type=resource_type,
                        resource_id=resource_id
                    )
                
                return result
                
            except Exception as e:
                # تسجيل النشاط الفاشل
                if hasattr(current_app, 'logger_system'):
                    current_app.logger_system.log_audit_event(
                        action=f"{action}_failed",
                        resource_type=resource_type,
                        resource_id=resource_id,
                        new_values={'error': str(e)}
                    )
                raise
        
        return decorated_function
    return decorator

# إنشاء مثيل عام من نظام التسجيل
logger_system = AdvancedLogger()
