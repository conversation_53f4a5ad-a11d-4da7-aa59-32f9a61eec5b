#!/usr/bin/env python3
"""
Blueprint للمصادقة وإدارة المستخدمين
Authentication and User Management Blueprint
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, current_app
from flask_login import login_user, logout_user, login_required, current_user
from datetime import datetime, timedelta
import re

from models import db, User, LoginAttempt, AuditLog
from security import security_manager, password_validator
from permissions import Permission, require_permission

auth_bp = Blueprint('auth', __name__)

def log_login_attempt(username, success, failure_reason=None):
    """تسجيل محاولة تسجيل الدخول"""
    attempt = LoginAttempt(
        username=username,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', ''),
        success=success,
        failure_reason=failure_reason
    )
    db.session.add(attempt)
    
    # تسجيل في سجل المراجعة أيضاً
    if success:
        audit_log = AuditLog(
            user_id=current_user.id if current_user.is_authenticated else None,
            action='login',
            resource_type='user',
            resource_id=current_user.id if current_user.is_authenticated else None,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(audit_log)
    
    db.session.commit()

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember = bool(request.form.get('remember'))
        
        # التحقق من البيانات الأساسية
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            log_login_attempt(username, False, 'missing_credentials')
            return render_template('auth/login.html')
        
        # التحقق من الحظر
        if security_manager.is_blocked(request.remote_addr):
            flash('تم حظر عنوان IP هذا مؤقتاً بسبب محاولات دخول متكررة', 'error')
            log_login_attempt(username, False, 'ip_blocked')
            return render_template('auth/login.html')
        
        # البحث عن المستخدم
        user = User.query.filter_by(username=username).first()
        
        if not user:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            security_manager.record_failed_attempt(request.remote_addr)
            log_login_attempt(username, False, 'invalid_username')
            return render_template('auth/login.html')
        
        # التحقق من حالة المستخدم
        if not user.is_active:
            flash('حسابك معطل، يرجى الاتصال بالمدير', 'error')
            log_login_attempt(username, False, 'account_disabled')
            return render_template('auth/login.html')
        
        # التحقق من الحظر المؤقت
        if user.is_locked():
            flash('حسابك محظور مؤقتاً، يرجى المحاولة لاحقاً', 'error')
            log_login_attempt(username, False, 'account_locked')
            return render_template('auth/login.html')
        
        # التحقق من كلمة المرور
        if not user.check_password(password):
            # زيادة عدد المحاولات الفاشلة
            user.failed_login_attempts += 1
            
            # حظر الحساب بعد 5 محاولات فاشلة
            if user.failed_login_attempts >= 5:
                user.lock_account(30)  # حظر لمدة 30 دقيقة
                flash('تم حظر حسابك لمدة 30 دقيقة بسبب محاولات دخول متكررة', 'error')
                log_login_attempt(username, False, 'account_locked_attempts')
            else:
                remaining = 5 - user.failed_login_attempts
                flash(f'كلمة المرور غير صحيحة. المحاولات المتبقية: {remaining}', 'error')
                log_login_attempt(username, False, 'invalid_password')
            
            security_manager.record_failed_attempt(request.remote_addr)
            db.session.commit()
            return render_template('auth/login.html')
        
        # تسجيل دخول ناجح
        user.failed_login_attempts = 0
        user.last_login = datetime.utcnow()
        user.locked_until = None
        
        login_user(user, remember=remember)
        
        # تسجيل النشاط
        log_login_attempt(username, True)
        
        db.session.commit()
        
        flash(f'مرحباً {user.username}!', 'success')
        
        # إعادة التوجيه للصفحة المطلوبة أو لوحة التحكم
        next_page = request.args.get('next')
        if next_page and next_page.startswith('/'):
            return redirect(next_page)
        return redirect(url_for('main.dashboard'))
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    # تسجيل النشاط
    audit_log = AuditLog(
        user_id=current_user.id,
        action='logout',
        resource_type='user',
        resource_id=current_user.id,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', '')
    )
    db.session.add(audit_log)
    db.session.commit()
    
    username = current_user.username
    logout_user()
    flash(f'تم تسجيل الخروج بنجاح. وداعاً {username}!', 'success')
    return redirect(url_for('main.index'))

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # التحقق من البيانات
        if not all([current_password, new_password, confirm_password]):
            flash('يرجى ملء جميع الحقول', 'error')
            return render_template('auth/change_password.html')
        
        # التحقق من كلمة المرور الحالية
        if not current_user.check_password(current_password):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return render_template('auth/change_password.html')
        
        # التحقق من تطابق كلمة المرور الجديدة
        if new_password != confirm_password:
            flash('كلمة المرور الجديدة غير متطابقة', 'error')
            return render_template('auth/change_password.html')
        
        # التحقق من قوة كلمة المرور
        is_valid, errors = password_validator.validate(new_password)
        if not is_valid:
            for error in errors:
                flash(error, 'error')
            return render_template('auth/change_password.html')
        
        # تحديث كلمة المرور
        try:
            old_hash = current_user.password_hash
            current_user.set_password(new_password)
            
            # تسجيل النشاط
            audit_log = AuditLog(
                user_id=current_user.id,
                action='update',
                resource_type='user',
                resource_id=current_user.id,
                old_values='{"password": "changed"}',
                new_values='{"password": "changed"}',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(audit_log)
            db.session.commit()
            
            flash('تم تغيير كلمة المرور بنجاح', 'success')
            return redirect(url_for('main.dashboard'))
            
        except ValueError as e:
            flash(str(e), 'error')
            return render_template('auth/change_password.html')
    
    return render_template('auth/change_password.html')

@auth_bp.route('/profile')
@login_required
def profile():
    """ملف المستخدم الشخصي"""
    return render_template('auth/profile.html', user=current_user)

@auth_bp.route('/users')
@login_required
@require_permission(Permission.VIEW_USERS)
def users():
    """قائمة المستخدمين"""
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('auth/users.html', users=users)

@auth_bp.route('/users/create', methods=['GET', 'POST'])
@login_required
@require_permission(Permission.CREATE_USER)
def create_user():
    """إنشاء مستخدم جديد"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        is_admin = bool(request.form.get('is_admin'))
        
        try:
            # إنشاء المستخدم
            user = User(
                username=username,
                email=email,
                is_admin=is_admin
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            # تسجيل النشاط
            audit_log = AuditLog(
                user_id=current_user.id,
                action='create',
                resource_type='user',
                resource_id=user.id,
                new_values=f'{{"username": "{username}", "email": "{email}", "is_admin": {is_admin}}}',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(audit_log)
            db.session.commit()
            
            flash(f'تم إنشاء المستخدم "{username}" بنجاح', 'success')
            return redirect(url_for('auth.users'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إنشاء المستخدم: {str(e)}', 'error')
    
    return render_template('auth/create_user.html')
