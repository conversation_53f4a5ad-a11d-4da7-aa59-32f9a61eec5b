import os
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask, render_template, request, redirect, url_for, flash
from flask_login import LoginManager
from datetime import datetime

from config import config
from models import db, User
from security import security_manager

def create_app(config_name=None):
    """إنشاء وتكوين التطبيق"""
    app = Flask(__name__)

    # تحميل التكوين
    config_name = config_name or os.environ.get('FLASK_CONFIG', 'default')
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # تهيئة قاعدة البيانات
    db.init_app(app)

    # إعداد Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # إعداد الأمان
    setup_security(app)

    # إعداد التسجيل
    setup_logging(app)

    # تسجيل Blueprints
    register_blueprints(app)

    # إعداد معالجات الأخطاء
    setup_error_handlers(app)

    # إعداد دوال القوالب
    setup_template_functions(app)

    # تهيئة قاعدة البيانات
    with app.app_context():
        db.create_all()
        create_default_admin()

    return app

def setup_security(app):
    """إعداد الأمان"""
    try:
        # إضافة رؤوس الأمان
        @app.after_request
        def add_security_headers(response):
            headers = app.config.get('SECURITY_HEADERS', {})
            for header, value in headers.items():
                response.headers[header] = value
            return response

        # إضافة CSRF token للقوالب
        @app.context_processor
        def inject_csrf_token():
            return dict(csrf_token=security_manager.generate_csrf_token)

    except Exception as e:
        app.logger.error(f"Error setting up security: {str(e)}")

def setup_logging(app):
    """إعداد نظام التسجيل"""
    if not app.debug and not app.testing:
        # إنشاء مجلد السجلات
        if not os.path.exists('logs'):
            os.mkdir('logs')

        # إعداد ملف السجل
        file_handler = RotatingFileHandler(
            app.config.get('LOG_FILE', 'logs/app.log'),
            maxBytes=10240000,  # 10MB
            backupCount=10
        )

        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))

        file_handler.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.addHandler(file_handler)
        app.logger.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        app.logger.info('Company Management System startup')

def register_blueprints(app):
    """تسجيل Blueprints"""
    try:
        from blueprints.main import main_bp
        from blueprints.auth import auth_bp
        from blueprints.companies import companies_bp

        app.register_blueprint(main_bp)
        app.register_blueprint(auth_bp, url_prefix='/auth')
        app.register_blueprint(companies_bp)

        # يمكن إضافة المزيد من blueprints هنا

    except ImportError as e:
        app.logger.error(f"Error importing blueprints: {str(e)}")
        # Fallback للنظام القديم
        setup_legacy_routes(app)

def setup_error_handlers(app):
    """إعداد معالجات الأخطاء"""
    @app.errorhandler(403)
    def forbidden(error):
        return render_template('errors/403.html'), 403

    @app.errorhandler(404)
    def not_found(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        app.logger.error(f'Server Error: {error}')
        return render_template('errors/500.html'), 500

    @app.errorhandler(413)
    def too_large(error):
        return render_template('errors/413.html'), 413

def setup_template_functions(app):
    """إعداد دوال القوالب"""
    @app.template_global()
    def current_year():
        return datetime.now().year

    @app.template_filter('arabic_date')
    def arabic_date_filter(date_obj):
        if not date_obj:
            return ""
        from utils import format_arabic_date
        return format_arabic_date(date_obj, include_time=True)

    @app.template_filter('file_size')
    def file_size_filter(size_bytes):
        if not size_bytes:
            return "0 B"
        from utils import format_file_size
        return format_file_size(size_bytes)

def create_default_admin():
    """إنشاء مدير افتراضي"""
    try:
        if not User.query.first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True
            )
            admin_user.set_password('Admin@123')  # كلمة مرور قوية
            db.session.add(admin_user)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / Admin@123")
    except Exception as e:
        print(f"خطأ في إنشاء المستخدم الافتراضي: {str(e)}")

def setup_legacy_routes(app):
    """إعداد المسارات القديمة كـ fallback"""
    from flask_login import login_user, logout_user, login_required, current_user

    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return render_template('index.html')

    @app.route('/dashboard')
    @login_required
    def dashboard():
        return render_template('dashboard.html', current_date=datetime.now())

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form['username']
            password = request.form['password']

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user, remember=True)
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template('login.html')

    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('index'))


if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)