#!/usr/bin/env python3
"""
أدوات مساعدة للنظام
Utility functions for the Company Management System
"""

import os
import re
import hashlib
from datetime import datetime
from pathlib import Path
from PIL import Image
import mimetypes

def validate_saudi_national_id(national_id):
    """التحقق من صحة رقم الهوية السعودية"""
    if not national_id or len(national_id) != 10:
        return False
    
    if not national_id.isdigit():
        return False
    
    # يجب أن يبدأ بـ 1 أو 2
    if not national_id.startswith(('1', '2')):
        return False
    
    # خوارزمية التحقق من رقم الهوية السعودية
    digits = [int(d) for d in national_id]
    
    # ضرب الأرقام في المواضع الفردية في 2
    for i in range(0, 9, 2):
        digits[i] *= 2
        if digits[i] > 9:
            digits[i] = digits[i] // 10 + digits[i] % 10
    
    # جمع جميع الأرقام
    total = sum(digits[:-1])
    
    # حساب رقم التحقق
    check_digit = (10 - (total % 10)) % 10
    
    return check_digit == digits[-1]

def validate_saudi_phone(phone):
    """التحقق من صحة رقم الهاتف السعودي"""
    if not phone:
        return False
    
    # إزالة المسافات والرموز
    phone = re.sub(r'[^\d]', '', phone)
    
    # يجب أن يكون 10 أرقام ويبدأ بـ 05
    if len(phone) == 10 and phone.startswith('05'):
        return True
    
    # أو 12 رقم ويبدأ بـ 9665
    if len(phone) == 12 and phone.startswith('9665'):
        return True
    
    return False

def format_saudi_phone(phone):
    """تنسيق رقم الهاتف السعودي"""
    if not phone:
        return phone
    
    phone = re.sub(r'[^\d]', '', phone)
    
    if len(phone) == 10 and phone.startswith('05'):
        return phone
    elif len(phone) == 9 and phone.startswith('5'):
        return '0' + phone
    elif len(phone) == 12 and phone.startswith('9665'):
        return '0' + phone[3:]
    
    return phone

def validate_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return True  # البريد الإلكتروني اختياري
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def generate_file_hash(file_path):
    """إنشاء hash للملف للتحقق من التكرار"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def get_file_info(file_path):
    """الحصول على معلومات الملف"""
    if not os.path.exists(file_path):
        return None
    
    stat = os.stat(file_path)
    mime_type, _ = mimetypes.guess_type(file_path)
    
    return {
        'size': stat.st_size,
        'created': datetime.fromtimestamp(stat.st_ctime),
        'modified': datetime.fromtimestamp(stat.st_mtime),
        'mime_type': mime_type,
        'extension': Path(file_path).suffix.lower()
    }

def resize_image(image_path, max_width=1920, max_height=1080, quality=85):
    """تصغير حجم الصورة"""
    try:
        with Image.open(image_path) as img:
            # التحقق من حاجة الصورة للتصغير
            if img.width <= max_width and img.height <= max_height:
                return False
            
            # حساب الأبعاد الجديدة مع الحفاظ على النسبة
            ratio = min(max_width / img.width, max_height / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            
            # تصغير الصورة
            img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # حفظ الصورة المصغرة
            if img.format == 'JPEG':
                img_resized.save(image_path, 'JPEG', quality=quality, optimize=True)
            else:
                img_resized.save(image_path, img.format, optimize=True)
            
            return True
    except Exception as e:
        print(f"خطأ في تصغير الصورة: {e}")
        return False

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def sanitize_filename(filename):
    """تنظيف اسم الملف من الأحرف غير المسموحة"""
    # إزالة الأحرف الخطيرة
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # إزالة المسافات الزائدة
    filename = re.sub(r'\s+', ' ', filename).strip()
    
    # التأكد من عدم تجاوز الطول المسموح
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

def generate_unique_filename(original_filename, upload_dir):
    """إنشاء اسم ملف فريد"""
    filename = sanitize_filename(original_filename)
    name, ext = os.path.splitext(filename)
    
    counter = 1
    new_filename = filename
    
    while os.path.exists(os.path.join(upload_dir, new_filename)):
        new_filename = f"{name}_{counter}{ext}"
        counter += 1
    
    return new_filename

def calculate_directory_size(directory):
    """حساب حجم المجلد"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size

def clean_old_files(directory, days_old=30):
    """حذف الملفات القديمة"""
    if not os.path.exists(directory):
        return 0
    
    cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
    deleted_count = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.getmtime(file_path) < cutoff_time:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except OSError:
                    pass
    
    return deleted_count

def format_arabic_date(date_obj, include_time=False):
    """تنسيق التاريخ بالعربية"""
    if not date_obj:
        return ""
    
    months_ar = [
        "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    ]
    
    day = date_obj.day
    month = months_ar[date_obj.month - 1]
    year = date_obj.year
    
    formatted_date = f"{day} {month} {year}"
    
    if include_time:
        hour = date_obj.hour
        minute = date_obj.minute
        formatted_date += f" - {hour:02d}:{minute:02d}"
    
    return formatted_date

def convert_to_arabic_numbers(text):
    """تحويل الأرقام الإنجليزية إلى عربية"""
    arabic_digits = '٠١٢٣٤٥٦٧٨٩'
    english_digits = '0123456789'
    
    for i, digit in enumerate(english_digits):
        text = text.replace(digit, arabic_digits[i])
    
    return text

def validate_company_name(name):
    """التحقق من صحة اسم الشركة"""
    if not name or len(name.strip()) < 3:
        return False, "اسم الشركة يجب أن يكون 3 أحرف على الأقل"
    
    if len(name) > 200:
        return False, "اسم الشركة طويل جداً (الحد الأقصى 200 حرف)"
    
    # التحقق من عدم وجود أحرف خطيرة
    dangerous_chars = ['<', '>', '"', "'", '&', ';']
    if any(char in name for char in dangerous_chars):
        return False, "اسم الشركة يحتوي على أحرف غير مسموحة"
    
    return True, ""

def log_activity(user_id, action, details=""):
    """تسجيل نشاط المستخدم (للتطوير المستقبلي)"""
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'user_id': user_id,
        'action': action,
        'details': details
    }
    
    # يمكن حفظ هذا في قاعدة البيانات أو ملف log
    print(f"LOG: {log_entry}")

def get_system_stats():
    """إحصائيات سريعة للنظام"""
    current_dir = Path(__file__).parent

    stats = {
        'timestamp': datetime.now().isoformat(),
        'database_exists': (current_dir / 'companies.db').exists(),
        'uploads_dir_exists': (current_dir / 'uploads').exists(),
        'backups_dir_exists': (current_dir / 'backups').exists(),
    }

    # حساب أحجام المجلدات
    if stats['uploads_dir_exists']:
        stats['uploads_size'] = calculate_directory_size(current_dir / 'uploads')

    if stats['backups_dir_exists']:
        stats['backups_size'] = calculate_directory_size(current_dir / 'backups')

    if stats['database_exists']:
        stats['database_size'] = (current_dir / 'companies.db').stat().st_size

    return stats

def save_uploaded_file(file, company_id):
    """حفظ الملف المرفوع وإرجاع معلوماته مع فحص أمني شامل"""
    from flask import current_app
    from file_scanner import file_scanner
    import uuid
    import tempfile

    if not file or not file.filename:
        return {'error': 'لم يتم اختيار ملف'}

    # تنظيف اسم الملف
    original_filename = sanitize_filename(file.filename)

    # التحقق من أمان الملف الأساسي
    if not is_allowed_file(original_filename):
        return {'error': 'نوع الملف غير مسموح'}

    try:
        # حفظ مؤقت للفحص
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name

        # فحص الملف أمنياً
        scan_results = file_scanner.scan_file(temp_path)

        if not scan_results['safe']:
            # حذف الملف المؤقت
            os.unlink(temp_path)

            # عزل الملف إذا كان خطيراً جداً
            threats = '; '.join(scan_results['threats'])
            return {'error': f'تم رفض الملف لأسباب أمنية: {threats}'}

        # إنشاء اسم ملف فريد
        file_extension = original_filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

        # إنشاء مجلد للشركة
        upload_dir = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        company_folder = os.path.join(upload_dir, f"company_{company_id}")
        os.makedirs(company_folder, exist_ok=True)

        # مسار الملف النهائي
        final_path = os.path.join(company_folder, unique_filename)

        # نقل الملف من المكان المؤقت
        os.rename(temp_path, final_path)

        # تحديد نوع الملف
        file_type = determine_file_type(file_extension)

        # حساب hash الملف
        file_hash = generate_file_hash(final_path)

        # تصغير الصورة إذا كانت كبيرة
        if file_type == 'image':
            try:
                resize_image(final_path)
            except Exception as e:
                current_app.logger.warning(f"فشل في تصغير الصورة: {e}")

        # معلومات الملف النهائية
        file_info = {
            'filename': unique_filename,
            'original_filename': original_filename,
            'file_path': final_path,
            'file_type': file_type,
            'file_size': os.path.getsize(final_path),
            'mime_type': scan_results['file_info'].get('mime_type', file.content_type),
            'file_hash': file_hash,
            'scan_warnings': scan_results.get('warnings', [])
        }

        # تسجيل التحذيرات إن وجدت
        if scan_results.get('warnings'):
            current_app.logger.info(f"تحذيرات فحص الملف {original_filename}: {scan_results['warnings']}")

        return file_info

    except Exception as e:
        # تنظيف الملف المؤقت في حالة الخطأ
        try:
            if 'temp_path' in locals():
                os.unlink(temp_path)
        except:
            pass

        current_app.logger.error(f"خطأ في حفظ الملف {original_filename}: {e}")
        return {'error': f'خطأ في حفظ الملف: {str(e)}'}

def validate_file_upload(file, max_size=None):
    """التحقق من صحة الملف المرفوع قبل الحفظ"""
    from flask import current_app

    if not file or not file.filename:
        return False, 'لم يتم اختيار ملف'

    # التحقق من حجم الملف
    max_size = max_size or current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)

    # قراءة حجم الملف
    file.seek(0, 2)  # الانتقال لنهاية الملف
    file_size = file.tell()
    file.seek(0)  # العودة للبداية

    if file_size > max_size:
        return False, f'حجم الملف كبير جداً. الحد الأقصى: {format_file_size(max_size)}'

    if file_size == 0:
        return False, 'الملف فارغ'

    # التحقق من اسم الملف
    if not is_allowed_file(file.filename):
        return False, 'نوع الملف غير مسموح'

    return True, 'الملف صالح'

def is_allowed_file(filename):
    """التحقق من أن الملف مسموح"""
    from flask import current_app

    if not filename or '.' not in filename:
        return False

    extension = filename.rsplit('.', 1)[1].lower()

    # التحقق من الامتدادات المحظورة
    forbidden_extensions = current_app.config.get('FORBIDDEN_EXTENSIONS', set())
    if extension in forbidden_extensions:
        return False

    # التحقق من الامتدادات المسموحة
    allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', set())
    return extension in allowed_extensions

def determine_file_type(extension):
    """تحديد نوع الملف بناءً على الامتداد"""
    image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'}
    document_extensions = {'pdf', 'doc', 'docx', 'txt', 'rtf'}
    spreadsheet_extensions = {'xls', 'xlsx', 'csv'}

    if extension in image_extensions:
        return 'image'
    elif extension in document_extensions:
        return 'document'
    elif extension in spreadsheet_extensions:
        return 'spreadsheet'
    else:
        return 'other'