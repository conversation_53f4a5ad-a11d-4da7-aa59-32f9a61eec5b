{% extends "base.html" %}

{% block title %}إدارة الأشخاص - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in-up">
    <div>
        <h2>
            <i class="bi bi-people text-info"></i>
            إدارة الأشخاص
        </h2>
        <small class="text-muted">إدارة وتنظيم بيانات الأشخاص والملاك</small>
    </div>
    <div class="action-buttons">
        <button class="btn btn-outline-primary" onclick="exportPeopleData()">
            <i class="bi bi-download me-2"></i>
            تصدير البيانات
        </button>
        <a href="{{ url_for('add_person') }}" class="btn btn-success">
            <i class="bi bi-person-plus me-2"></i>
            إضافة شخص جديد
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.1s;">
        <div class="card text-center stats-card">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-people display-6"></i>
                </div>
                <h3 class="mb-1">{{ people|length }}</h3>
                <p class="text-white-50 mb-0">إجمالي الأشخاص</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.2s;">
        <div class="card text-center stats-card stats-card-success">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-building display-6"></i>
                </div>
                <h3 class="mb-1">{{ people|selectattr('owned_companies')|list|length }}</h3>
                <p class="text-white-50 mb-0">ملاك الشركات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.3s;">
        <div class="card text-center stats-card stats-card-warning">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-telephone display-6"></i>
                </div>
                <h3 class="mb-1">{{ people|selectattr('phone')|list|length }}</h3>
                <p class="text-white-50 mb-0">لديهم هاتف</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.4s;">
        <div class="card text-center stats-card stats-card-info">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-envelope display-6"></i>
                </div>
                <h3 class="mb-1">{{ people|selectattr('email')|list|length }}</h3>
                <p class="text-white-50 mb-0">لديهم إيميل</p>
            </div>
        </div>
    </div>
</div>

{% if people %}
<div class="card slide-in-right" style="animation-delay: 0.5s;">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul text-primary me-2"></i>
                    قائمة الأشخاص 
                    <span class="badge bg-primary ms-2">{{ people|length }}</span>
                </h5>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو رقم الهوية أو الهاتف...">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="peopleTable">
                <thead class="table-light">
                    <tr>
                        <th>الاسم</th>
                        <th>رقم الهوية</th>
                        <th>الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>الشركات المملوكة</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for person in people %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="avatar-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; border-radius: 50%; font-weight: bold;">
                                        {{ person.name[0].upper() }}
                                    </div>
                                </div>
                                <div>
                                    <strong class="text-dark">{{ person.name }}</strong>
                                    {% if person.notes %}
                                    <br><small class="text-muted">{{ person.notes[:50] }}{% if person.notes|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if person.national_id %}
                            <span class="badge bg-light text-dark border">
                                <i class="bi bi-card-text me-1"></i>
                                {{ person.national_id }}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="bi bi-dash-circle"></i> غير محدد
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if person.phone %}
                            <a href="tel:{{ person.phone }}" class="text-decoration-none">
                                <i class="bi bi-telephone text-success me-1"></i>
                                {{ person.phone }}
                            </a>
                            {% else %}
                            <span class="text-muted">
                                <i class="bi bi-telephone-x"></i> غير محدد
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if person.email %}
                            <a href="mailto:{{ person.email }}" class="text-decoration-none">
                                <i class="bi bi-envelope text-info me-1"></i>
                                {{ person.email }}
                            </a>
                            {% else %}
                            <span class="text-muted">
                                <i class="bi bi-envelope-x"></i> غير محدد
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex flex-wrap gap-1">
                                {% for company in person.owned_companies %}
                                {% if company.is_active %}
                                <a href="{{ url_for('company_details', company_id=company.id) }}" class="badge bg-primary text-decoration-none" title="شركة مملوكة">
                                    <i class="bi bi-building me-1"></i>{{ company.name }}
                                </a>
                                {% endif %}
                                {% endfor %}
                                {% if person.owned_companies|selectattr('is_active')|list|length == 0 %}
                                <span class="text-muted">
                                    <i class="bi bi-building-x"></i> لا يملك شركات
                                </span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ person.created_at.strftime('%Y-%m-%d') }}
                            </small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        data-bs-toggle="modal" data-bs-target="#personModal{{ person.id }}"
                                        title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <a href="{{ url_for('edit_person', person_id=person.id) }}" class="btn btn-sm btn-outline-warning"
                                   title="تعديل البيانات">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        title="حذف الشخص"
                                        onclick="confirmDeletePerson('{{ person.name }}', '{{ url_for('delete_person', person_id=person.id) }}')">
                                    <i class="bi bi-trash3"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Person Details Modals -->
{% for person in people %}
<div class="modal fade" id="personModal{{ person.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title">
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; border-radius: 50%; font-weight: bold;">
                            {{ person.name[0].upper() }}
                        </div>
                        <div>
                            <strong>{{ person.name }}</strong>
                            <br><small class="text-muted">تفاصيل الشخص</small>
                        </div>
                    </div>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i class="bi bi-person-vcard me-2"></i>
                            المعلومات الشخصية
                        </h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>الاسم الكامل:</strong></td>
                                <td>{{ person.name }}</td>
                            </tr>
                            {% if person.national_id %}
                            <tr>
                                <td><strong>رقم الهوية:</strong></td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ person.national_id }}</span>
                                </td>
                            </tr>
                            {% endif %}
                            {% if person.phone %}
                            <tr>
                                <td><strong>الهاتف:</strong></td>
                                <td>
                                    <a href="tel:{{ person.phone }}" class="text-decoration-none">
                                        <i class="bi bi-telephone text-success me-1"></i>{{ person.phone }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            {% if person.email %}
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>
                                    <a href="mailto:{{ person.email }}" class="text-decoration-none">
                                        <i class="bi bi-envelope text-info me-1"></i>{{ person.email }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            {% if person.address %}
                            <tr>
                                <td><strong>العنوان:</strong></td>
                                <td>{{ person.address }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>تاريخ التسجيل:</strong></td>
                                <td>{{ person.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-success">
                            <i class="bi bi-building me-2"></i>
                            الشركات المملوكة
                        </h6>
                        {% if person.owned_companies|selectattr('is_active')|list %}
                        <div class="list-group list-group-flush">
                            {% for company in person.owned_companies %}
                            {% if company.is_active %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ company.name }}</strong>
                                    <br><small class="text-muted">{{ company.capital_category.name }} - {{ "{:,}".format(company.capital_category.amount) }} دينار ليبي</small>
                                </div>
                                <a href="{{ url_for('company_details', company_id=company.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="bi bi-building-x display-6"></i>
                            <p class="mt-2">لا يملك أي شركات حالياً</p>
                        </div>
                        {% endif %}
                        
                        {% if person.notes %}
                        <h6 class="text-warning mt-3">
                            <i class="bi bi-sticky me-2"></i>
                            ملاحظات
                        </h6>
                        <div class="alert alert-light">
                            {{ person.notes }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="{{ url_for('edit_person', person_id=person.id) }}" class="btn btn-warning">
                    <i class="bi bi-pencil-square me-2"></i>
                    تعديل البيانات
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}

{% else %}
<div class="text-center py-5 fade-in-up">
    <div class="mb-4">
        <i class="bi bi-people display-1 text-muted opacity-50"></i>
    </div>
    <h3 class="mt-3 text-muted">لا يوجد أشخاص مسجلين</h3>
    <p class="text-muted mb-4">ابدأ بإضافة أول شخص في النظام لإدارة بيانات الملاك والشركاء</p>
    <a href="{{ url_for('add_person') }}" class="btn btn-success btn-lg">
        <i class="bi bi-person-plus me-2"></i>
        إضافة شخص جديد
    </a>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// تحسين البحث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#peopleTable tbody tr');
            let visibleCount = 0;
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // تحديث عداد النتائج
            updateResultsCount(visibleCount, tableRows.length);
        });
    }
});

// تحديث عداد النتائج
function updateResultsCount(visible, total) {
    const badge = document.querySelector('.badge.bg-primary');
    if (badge) {
        badge.textContent = visible === total ? total : `${visible} من ${total}`;
    }
}

// دالة تأكيد حذف الشخص
function confirmDeletePerson(personName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف الشخص "${personName}"؟\n\nسيتم حذف جميع البيانات المرتبطة به.`)) {
        // إنشاء نموذج مخفي للحذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        document.body.appendChild(form);
        form.submit();
    }
}

// تصدير بيانات الأشخاص
async function exportPeopleData() {
    try {
        const response = await fetch('/api/people/export');
        const data = await response.json();
        
        // إنشاء ملف CSV
        const csvData = [
            ['الاسم', 'رقم الهوية', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'عدد الشركات', 'تاريخ التسجيل'],
            ...data.people.map(person => [
                person.name,
                person.national_id || '',
                person.phone || '',
                person.email || '',
                person.address || '',
                person.companies_count || 0,
                person.created_at
            ])
        ];
        
        const csvContent = csvData.map(row => 
            row.map(cell => `"${cell}"`).join(',')
        ).join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `الأشخاص_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        
        showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير البيانات', 'error');
    }
}

// ترتيب الجدول
function sortTable(columnIndex) {
    const table = document.getElementById('peopleTable');
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        return aText.localeCompare(bText, 'ar', { numeric: true });
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للصفوف
    const tableRows = document.querySelectorAll('#peopleTable tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.01)';
            this.style.transition = 'all 0.2s ease';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // تحسين النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            this.style.animation = 'fadeInUp 0.3s ease';
        });
    });
});

// دالة عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
    
    const data = [];
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        data.push({
            'الاسم': cells[0].textContent.trim().split('\n')[0],
            'رقم الهوية': cells[1].textContent.trim(),
            'الهاتف': cells[2].textContent.trim(),
            'البريد الإلكتروني': cells[3].textContent.trim(),
            'الشركات المملوكة': cells[4].textContent.trim(),
            'تاريخ التسجيل': cells[5].textContent.trim()
        });
    });
    
    // تحويل إلى CSV
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');
    
    // تحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `الأشخاص_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    
    // عرض رسالة نجاح
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    alert.innerHTML = `
        تم تصدير بيانات ${data.length} شخص بنجاح
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) alert.remove();
    }, 5000);
}
</script>
{% endblock %}