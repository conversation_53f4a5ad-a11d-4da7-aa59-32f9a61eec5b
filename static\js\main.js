/**
 * الملف الرئيسي للـ JavaScript
 * Main JavaScript File
 */

// إعدادات عامة
const AppConfig = {
    confirmDeleteMessage: 'هل أنت متأكد من الحذف؟ لا يمكن التراجع عن هذا الإجراء.',
    loadingMessage: 'جاري التحميل...',
    errorMessage: 'حدث خطأ غير متوقع',
    successMessage: 'تم بنجاح',
    maxFileSize: 16 * 1024 * 1024, // 16MB
    allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'xls', 'xlsx']
};

// فئة إدارة التطبيق الرئيسية
class AppManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupFormValidation();
        this.setupFileUpload();
        this.setupSearch();
        this.setupNotifications();
    }

    setupEventListeners() {
        // معالجة أزرار الحذف
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-delete, .btn-delete *')) {
                e.preventDefault();
                this.handleDelete(e.target.closest('.btn-delete'));
            }
        });

        // معالجة النماذج
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-validate="true"]')) {
                if (!this.validateForm(e.target)) {
                    e.preventDefault();
                }
            }
        });

        // معالجة رفع الملفات
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="file"]')) {
                this.handleFileSelect(e.target);
            }
        });
    }

    initializeComponents() {
        // تهيئة التلميحات
        this.initTooltips();
        
        // تهيئة النوافذ المنبثقة
        this.initModals();
        
        // تهيئة الجداول
        this.initTables();
        
        // تهيئة الرسوم البيانية
        this.initCharts();
    }

    initTooltips() {
        // تفعيل Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initModals() {
        // تفعيل Bootstrap modals
        const modalTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]'));
        modalTriggerList.forEach(modalTriggerEl => {
            modalTriggerEl.addEventListener('click', (e) => {
                const targetModal = document.querySelector(modalTriggerEl.getAttribute('data-bs-target'));
                if (targetModal) {
                    const modal = new bootstrap.Modal(targetModal);
                    modal.show();
                }
            });
        });
    }

    initTables() {
        // تحسين الجداول
        const tables = document.querySelectorAll('.table-responsive table');
        tables.forEach(table => {
            // إضافة فرز للأعمدة
            this.addTableSorting(table);
            
            // إضافة تصفية
            this.addTableFiltering(table);
        });
    }

    addTableSorting(table) {
        const headers = table.querySelectorAll('th[data-sortable="true"]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="bi bi-arrow-down-up text-muted"></i>';
            
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    }

    sortTable(table, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');

        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            // محاولة التحويل لرقم
            const aNum = parseFloat(aText);
            const bNum = parseFloat(bText);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? aNum - bNum : bNum - aNum;
            }
            
            return isAscending ? aText.localeCompare(bText, 'ar') : bText.localeCompare(aText, 'ar');
        });

        // إعادة ترتيب الصفوف
        rows.forEach(row => tbody.appendChild(row));

        // تحديث أيقونة الفرز
        table.querySelectorAll('th i').forEach(icon => {
            icon.className = 'bi bi-arrow-down-up text-muted';
        });
        
        const icon = header.querySelector('i');
        icon.className = isAscending ? 'bi bi-arrow-up text-primary' : 'bi bi-arrow-down text-primary';
        
        header.classList.toggle('sort-asc', isAscending);
        header.classList.toggle('sort-desc', !isAscending);
    }

    addTableFiltering(table) {
        const filterInput = table.parentElement.querySelector('.table-filter');
        if (filterInput) {
            filterInput.addEventListener('input', (e) => {
                this.filterTable(table, e.target.value);
            });
        }
    }

    filterTable(table, searchTerm) {
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(searchTerm.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    }

    handleDelete(button) {
        const message = button.getAttribute('data-confirm') || AppConfig.confirmDeleteMessage;
        
        if (confirm(message)) {
            const form = button.closest('form');
            if (form) {
                this.showLoading(button);
                form.submit();
            } else {
                // إذا لم يكن هناك نموذج، استخدم الرابط
                const href = button.getAttribute('href') || button.getAttribute('data-href');
                if (href) {
                    window.location.href = href;
                }
            }
        }
    }

    showLoading(element) {
        const originalText = element.innerHTML;
        element.innerHTML = '<i class="bi bi-hourglass-split"></i> ' + AppConfig.loadingMessage;
        element.disabled = true;
        
        // استعادة النص الأصلي بعد 10 ثوان (في حالة عدم إعادة التحميل)
        setTimeout(() => {
            element.innerHTML = originalText;
            element.disabled = false;
        }, 10000);
    }

    setupFormValidation() {
        // التحقق من النماذج
        const forms = document.querySelectorAll('form[data-validate="true"]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const errors = [];

        // التحقق من الحقول المطلوبة
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        // التحقق من البريد الإلكتروني
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                this.showFieldError(field, 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
        });

        // التحقق من أرقام الهاتف
        const phoneFields = form.querySelectorAll('input[data-type="phone"]');
        phoneFields.forEach(field => {
            if (field.value && !this.isValidPhone(field.value)) {
                this.showFieldError(field, 'رقم الهاتف غير صحيح');
                isValid = false;
            }
        });

        // التحقق من كلمات المرور
        const passwordFields = form.querySelectorAll('input[type="password"][data-validate="true"]');
        passwordFields.forEach(field => {
            if (field.value && !this.isValidPassword(field.value)) {
                this.showFieldError(field, 'كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام');
                isValid = false;
            }
        });

        return isValid;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isValidPhone(phone) {
        // تنظيف رقم الهاتف
        const cleanPhone = phone.replace(/\D/g, '');
        
        // التحقق من الأرقام السعودية
        return (cleanPhone.length === 10 && cleanPhone.startsWith('05')) ||
               (cleanPhone.length === 12 && cleanPhone.startsWith('9665'));
    }

    isValidPassword(password) {
        // كلمة المرور يجب أن تحتوي على:
        // - 8 أحرف على الأقل
        // - حرف كبير واحد على الأقل
        // - حرف صغير واحد على الأقل
        // - رقم واحد على الأقل
        const minLength = password.length >= 8;
        const hasUpper = /[A-Z]/.test(password);
        const hasLower = /[a-z]/.test(password);
        const hasNumber = /\d/.test(password);
        
        return minLength && hasUpper && hasLower && hasNumber;
    }

    setupFileUpload() {
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.handleFileSelect(e.target);
            });
        });
    }

    handleFileSelect(input) {
        const files = input.files;
        const feedback = input.parentNode.querySelector('.file-feedback') || this.createFileFeedback(input);
        
        if (files.length === 0) {
            feedback.innerHTML = '';
            return;
        }

        let feedbackHTML = '';
        let hasErrors = false;

        Array.from(files).forEach(file => {
            const validation = this.validateFile(file);
            
            if (validation.valid) {
                feedbackHTML += `<div class="text-success"><i class="bi bi-check-circle"></i> ${file.name} (${this.formatFileSize(file.size)})</div>`;
            } else {
                feedbackHTML += `<div class="text-danger"><i class="bi bi-x-circle"></i> ${file.name}: ${validation.error}</div>`;
                hasErrors = true;
            }
        });

        feedback.innerHTML = feedbackHTML;
        
        // تعطيل زر الإرسال إذا كان هناك أخطاء
        const submitBtn = input.closest('form').querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = hasErrors;
        }
    }

    createFileFeedback(input) {
        const feedback = document.createElement('div');
        feedback.className = 'file-feedback mt-2';
        input.parentNode.appendChild(feedback);
        return feedback;
    }

    validateFile(file) {
        // التحقق من حجم الملف
        if (file.size > AppConfig.maxFileSize) {
            return {
                valid: false,
                error: `حجم الملف كبير جداً. الحد الأقصى: ${this.formatFileSize(AppConfig.maxFileSize)}`
            };
        }

        // التحقق من نوع الملف
        const extension = file.name.split('.').pop().toLowerCase();
        if (!AppConfig.allowedFileTypes.includes(extension)) {
            return {
                valid: false,
                error: `نوع الملف غير مسموح. الأنواع المسموحة: ${AppConfig.allowedFileTypes.join(', ')}`
            };
        }

        return { valid: true };
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    setupSearch() {
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.performSearch(e.target.value, e.target);
                }, 300);
            });
        });
    }

    performSearch(query, input) {
        if (query.length < 2) {
            this.hideSearchResults(input);
            return;
        }

        // إرسال طلب البحث
        fetch(`/api/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(results => {
                this.showSearchResults(results, input);
            })
            .catch(error => {
                console.error('خطأ في البحث:', error);
            });
    }

    showSearchResults(results, input) {
        let resultsContainer = input.parentNode.querySelector('.search-results');
        
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.className = 'search-results position-absolute bg-white border rounded shadow-sm w-100';
            resultsContainer.style.zIndex = '1000';
            input.parentNode.style.position = 'relative';
            input.parentNode.appendChild(resultsContainer);
        }

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="p-3 text-muted">لا توجد نتائج</div>';
        } else {
            const resultsHTML = results.map(result => `
                <a href="${result.url}" class="d-block p-3 text-decoration-none border-bottom">
                    <div class="fw-bold">${result.title}</div>
                    <small class="text-muted">${result.description}</small>
                </a>
            `).join('');
            
            resultsContainer.innerHTML = resultsHTML;
        }

        resultsContainer.style.display = 'block';
    }

    hideSearchResults(input) {
        const resultsContainer = input.parentNode.querySelector('.search-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }

    setupNotifications() {
        // إخفاء الإشعارات تلقائياً
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    }

    initCharts() {
        // تهيئة الرسوم البيانية إذا كانت موجودة
        const chartElements = document.querySelectorAll('[data-chart]');
        chartElements.forEach(element => {
            this.createChart(element);
        });
    }

    createChart(element) {
        const chartType = element.getAttribute('data-chart');
        const chartData = JSON.parse(element.getAttribute('data-chart-data') || '{}');
        
        // يمكن استخدام مكتبة رسوم بيانية مثل Chart.js هنا
        console.log('إنشاء رسم بياني:', chartType, chartData);
    }

    // دوال مساعدة عامة
    showToast(message, type = 'info') {
        // إنشاء toast notification
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // إضافة للصفحة
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // إظهار التوست
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // إزالة التوست بعد الإخفاء
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AppManager();
});

// دوال مساعدة عامة
window.confirmDelete = function(message) {
    return confirm(message || AppConfig.confirmDeleteMessage);
};

window.showLoading = function(element) {
    if (window.app) {
        window.app.showLoading(element);
    }
};

window.showToast = function(message, type) {
    if (window.app) {
        window.app.showToast(message, type);
    }
};
