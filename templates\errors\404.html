{% extends "base.html" %}

{% block title %}الصفحة غير موجودة - 404{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <h1 class="display-1 text-primary">404</h1>
                <h2 class="mb-4">الصفحة غير موجودة</h2>
                <p class="lead mb-4">
                    عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
                </p>
                <div class="mb-4">
                    <i class="bi bi-search display-4 text-info"></i>
                </div>
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                        <i class="bi bi-house"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        رجوع
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.error-page h1 {
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-page .display-4 {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}
</style>
{% endblock %}
