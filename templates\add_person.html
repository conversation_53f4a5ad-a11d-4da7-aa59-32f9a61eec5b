{% extends "base.html" %}

{% block title %}إضافة شخص جديد - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-person-plus"></i>
                    إضافة شخص جديد
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   placeholder="الاسم الكامل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id"
                                   placeholder="1234567890" maxlength="10">
                            <div class="form-text">رقم الهوية الوطنية (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   placeholder="05xxxxxxxx">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"
                                  placeholder="العنوان الكامل..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('people') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i>
                            إضافة الشخص
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle"></i>
                    نصائح مهمة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>رقم الهوية:</strong> يُستخدم للتحقق من عدم تكرار الأشخاص في النظام
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>المعلومات الاختيارية:</strong> يمكن إضافتها لاحقاً أو تركها فارغة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>ربط الشركات:</strong> سيتم ربط الشخص تلقائياً عند إضافة شركة جديدة
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>التعديل:</strong> يمكن تعديل جميع البيانات لاحقاً من قائمة الأشخاص
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Format national ID input
document.getElementById('national_id').addEventListener('input', function() {
    // Remove non-digits
    let value = this.value.replace(/\D/g, '');
    
    // Limit to 10 digits
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    
    this.value = value;
    
    // Validate Saudi national ID format
    if (value.length === 10) {
        const firstDigit = parseInt(value.charAt(0));
        if (firstDigit !== 1 && firstDigit !== 2) {
            this.setCustomValidity('رقم الهوية يجب أن يبدأ بـ 1 أو 2');
        } else {
            this.setCustomValidity('');
        }
    } else if (value.length > 0) {
        this.setCustomValidity('رقم الهوية يجب أن يكون 10 أرقام');
    } else {
        this.setCustomValidity('');
    }
});

// Format phone number
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    
    // Auto-add 05 prefix for Saudi numbers
    if (value.length > 0 && !value.startsWith('05')) {
        if (value.startsWith('5')) {
            value = '0' + value;
        }
    }
    
    // Limit to 10 digits
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    
    this.value = value;
    
    // Validate Saudi phone format
    if (value.length === 10 && value.startsWith('05')) {
        this.setCustomValidity('');
    } else if (value.length > 0) {
        this.setCustomValidity('رقم الهاتف يجب أن يبدأ بـ 05 ويكون 10 أرقام');
    } else {
        this.setCustomValidity('');
    }
});

// Email validation
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value;
    if (email && !email.includes('@')) {
        this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
    } else {
        this.setCustomValidity('');
    }
});

// Check for duplicate national ID (you can implement AJAX call here)
document.getElementById('national_id').addEventListener('blur', function() {
    const nationalId = this.value;
    if (nationalId.length === 10) {
        // Here you could add AJAX call to check for duplicates
        console.log('Checking for duplicate national ID:', nationalId);
    }
});

// Auto-capitalize name
document.getElementById('name').addEventListener('input', function() {
    // Simple capitalization for Arabic names
    let value = this.value;
    // You can add more sophisticated Arabic name formatting here
    this.value = value;
});
</script>
{% endblock %}