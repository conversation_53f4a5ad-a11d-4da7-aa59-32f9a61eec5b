{% extends "base.html" %}

{% block title %}إضافة شخص جديد - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in-up">
    <div>
        <h2>
            <i class="bi bi-person-plus text-success"></i>
            إضافة شخص جديد
        </h2>
        <small class="text-muted">إضافة شخص جديد إلى قاعدة البيانات</small>
    </div>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('people') }}">الأشخاص</a></li>
            <li class="breadcrumb-item active">إضافة جديد</li>
        </ol>
    </nav>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow-lg border-0 slide-in-right">
            <div class="card-header bg-gradient bg-success text-white py-3">
                <h4 class="mb-0 fw-bold">
                    <i class="bi bi-person-vcard me-2"></i>
                    بيانات الشخص الجديد
                </h4>
                <small class="opacity-75">يرجى ملء البيانات المطلوبة بدقة</small>
            </div>
            <div class="card-body p-4">
                <form method="POST" id="personForm">
                    <!-- المعلومات الأساسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-person-circle me-2"></i>
                                المعلومات الأساسية
                            </h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label fw-bold">
                                <i class="bi bi-person text-success me-1"></i>
                                الاسم الكامل *
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="أدخل الاسم الكامل" autocomplete="name">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                الاسم كما يظهر في الوثائق الرسمية
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label fw-bold">
                                <i class="bi bi-card-text text-info me-1"></i>
                                رقم الهوية الوطنية
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-card-text"></i>
                                </span>
                                <input type="text" class="form-control" id="national_id" name="national_id"
                                       placeholder="1234567890123" maxlength="13" pattern="[0-9]{10,13}">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                رقم الهوية الوطنية الليبية (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات الاتصال -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-telephone me-2"></i>
                                معلومات الاتصال
                            </h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label fw-bold">
                                <i class="bi bi-telephone text-success me-1"></i>
                                رقم الهاتف
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-telephone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       placeholder="0912345678" pattern="[0-9]{10}" dir="ltr">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                رقم الهاتف المحمول (مثال: 0912345678)
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label fw-bold">
                                <i class="bi bi-envelope text-info me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email"
                                       placeholder="<EMAIL>" dir="ltr">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                البريد الإلكتروني للتواصل (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-geo-alt me-2"></i>
                                معلومات إضافية
                            </h5>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label fw-bold">
                                <i class="bi bi-geo-alt text-warning me-1"></i>
                                العنوان
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-geo-alt"></i>
                                </span>
                                <textarea class="form-control" id="address" name="address" rows="2"
                                          placeholder="العنوان الكامل (المدينة، المنطقة، الشارع...)"></textarea>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                العنوان السكني أو عنوان العمل
                            </div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="bi bi-sticky text-secondary me-1"></i>
                                ملاحظات
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-sticky"></i>
                                </span>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="أي ملاحظات إضافية أو معلومات مهمة..."></textarea>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                ملاحظات خاصة أو معلومات إضافية مفيدة
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center bg-light rounded p-3">
                                <div class="text-muted">
                                    <i class="bi bi-info-circle me-2"></i>
                                    تأكد من صحة البيانات قبل الحفظ
                                </div>
                                <div class="action-buttons">
                                    <a href="{{ url_for('people') }}" class="btn btn-outline-secondary me-2">
                                        <i class="bi bi-x-circle me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="reset" class="btn btn-outline-warning me-2" onclick="clearForm()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-success" id="submitBtn">
                                        <i class="bi bi-person-plus me-1"></i>
                                        إضافة الشخص
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle"></i>
                    نصائح مهمة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>رقم الهوية:</strong> يُستخدم للتحقق من عدم تكرار الأشخاص في النظام
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>المعلومات الاختيارية:</strong> يمكن إضافتها لاحقاً أو تركها فارغة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>ربط الشركات:</strong> سيتم ربط الشخص تلقائياً عند إضافة شركة جديدة
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check-circle text-success"></i>
                        <strong>التعديل:</strong> يمكن تعديل جميع البيانات لاحقاً من قائمة الأشخاص
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Format national ID input
document.getElementById('national_id').addEventListener('input', function() {
    // Remove non-digits
    let value = this.value.replace(/\D/g, '');
    
    // Limit to 10 digits
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    
    this.value = value;
    
    // Validate Saudi national ID format
    if (value.length === 10) {
        const firstDigit = parseInt(value.charAt(0));
        if (firstDigit !== 1 && firstDigit !== 2) {
            this.setCustomValidity('رقم الهوية يجب أن يبدأ بـ 1 أو 2');
        } else {
            this.setCustomValidity('');
        }
    } else if (value.length > 0) {
        this.setCustomValidity('رقم الهوية يجب أن يكون 10 أرقام');
    } else {
        this.setCustomValidity('');
    }
});

// Format phone number
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    
    // Auto-add 05 prefix for Saudi numbers
    if (value.length > 0 && !value.startsWith('05')) {
        if (value.startsWith('5')) {
            value = '0' + value;
        }
    }
    
    // Limit to 10 digits
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    
    this.value = value;
    
    // Validate Saudi phone format
    if (value.length === 10 && value.startsWith('05')) {
        this.setCustomValidity('');
    } else if (value.length > 0) {
        this.setCustomValidity('رقم الهاتف يجب أن يبدأ بـ 05 ويكون 10 أرقام');
    } else {
        this.setCustomValidity('');
    }
});

// Email validation
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value;
    if (email && !email.includes('@')) {
        this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
    } else {
        this.setCustomValidity('');
    }
});

// Check for duplicate national ID (you can implement AJAX call here)
document.getElementById('national_id').addEventListener('blur', function() {
    const nationalId = this.value;
    if (nationalId.length === 10) {
        // Here you could add AJAX call to check for duplicates
        console.log('Checking for duplicate national ID:', nationalId);
    }
});

// Auto-capitalize name
document.getElementById('name').addEventListener('input', function() {
    // Simple capitalization for Arabic names
    let value = this.value;
    // You can add more sophisticated Arabic name formatting here
    this.value = value;
});
</script>
{% endblock %}