#!/usr/bin/env python3
"""
نظام الأمان والحماية
Security and Protection System
"""

import re
import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app, session, abort
from flask_login import current_user
import bcrypt

class PasswordValidator:
    """فئة التحقق من قوة كلمات المرور"""
    
    def __init__(self, min_length=8, require_uppercase=True, require_lowercase=True, 
                 require_numbers=True, require_special=True):
        self.min_length = min_length
        self.require_uppercase = require_uppercase
        self.require_lowercase = require_lowercase
        self.require_numbers = require_numbers
        self.require_special = require_special
    
    def validate(self, password):
        """التحقق من صحة كلمة المرور"""
        errors = []
        
        if len(password) < self.min_length:
            errors.append(f'كلمة المرور يجب أن تكون {self.min_length} أحرف على الأقل')
        
        if self.require_uppercase and not re.search(r'[A-Z]', password):
            errors.append('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل')
        
        if self.require_lowercase and not re.search(r'[a-z]', password):
            errors.append('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل')
        
        if self.require_numbers and not re.search(r'\d', password):
            errors.append('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل')
        
        if self.require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل')
        
        # التحقق من كلمات المرور الشائعة
        common_passwords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'admin', 'admin123', 'password123', '12345678', 'welcome'
        ]
        
        if password.lower() in common_passwords:
            errors.append('كلمة المرور ضعيفة جداً، يرجى اختيار كلمة مرور أقوى')
        
        return len(errors) == 0, errors
    
    def generate_strong_password(self, length=12):
        """إنشاء كلمة مرور قوية"""
        import string
        
        # ضمان وجود كل نوع من الأحرف
        password = []
        password.append(secrets.choice(string.ascii_uppercase))
        password.append(secrets.choice(string.ascii_lowercase))
        password.append(secrets.choice(string.digits))
        password.append(secrets.choice('!@#$%^&*'))
        
        # إضافة باقي الأحرف عشوائياً
        all_chars = string.ascii_letters + string.digits + '!@#$%^&*'
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))
        
        # خلط الأحرف
        secrets.SystemRandom().shuffle(password)
        return ''.join(password)

class SecurityManager:
    """مدير الأمان العام"""
    
    def __init__(self):
        self.failed_attempts = {}
        self.blocked_ips = {}
    
    def hash_password(self, password):
        """تشفير كلمة المرور باستخدام bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt)
    
    def verify_password(self, password, hashed):
        """التحقق من كلمة المرور"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed)
    
    def generate_csrf_token(self):
        """إنشاء رمز CSRF"""
        if 'csrf_token' not in session:
            session['csrf_token'] = secrets.token_urlsafe(32)
        return session['csrf_token']
    
    def validate_csrf_token(self, token):
        """التحقق من رمز CSRF"""
        return token and session.get('csrf_token') == token
    
    def record_failed_attempt(self, identifier):
        """تسجيل محاولة دخول فاشلة"""
        now = datetime.now()
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        
        self.failed_attempts[identifier].append(now)
        
        # إزالة المحاولات القديمة (أكثر من ساعة)
        cutoff = now - timedelta(hours=1)
        self.failed_attempts[identifier] = [
            attempt for attempt in self.failed_attempts[identifier] 
            if attempt > cutoff
        ]
    
    def is_blocked(self, identifier, max_attempts=5, block_duration=30):
        """التحقق من حظر المستخدم/IP"""
        if identifier in self.blocked_ips:
            if datetime.now() < self.blocked_ips[identifier]:
                return True
            else:
                del self.blocked_ips[identifier]
        
        if identifier in self.failed_attempts:
            recent_attempts = len(self.failed_attempts[identifier])
            if recent_attempts >= max_attempts:
                # حظر لمدة محددة
                self.blocked_ips[identifier] = datetime.now() + timedelta(minutes=block_duration)
                return True
        
        return False
    
    def sanitize_filename(self, filename):
        """تنظيف اسم الملف من الأحرف الخطيرة"""
        # إزالة الأحرف الخطيرة
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # إزالة المسافات الزائدة والنقاط في البداية والنهاية
        filename = re.sub(r'^\.+|\.+$', '', filename.strip())
        
        # التأكد من عدم تجاوز الطول المسموح
        if len(filename) > current_app.config.get('MAX_FILENAME_LENGTH', 255):
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            max_name_length = current_app.config.get('MAX_FILENAME_LENGTH', 255) - len(ext) - 1
            filename = name[:max_name_length] + ('.' + ext if ext else '')
        
        return filename
    
    def is_safe_file(self, filename):
        """التحقق من أمان الملف"""
        if not filename:
            return False
        
        # التحقق من الامتداد
        extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        
        forbidden_extensions = current_app.config.get('FORBIDDEN_EXTENSIONS', set())
        allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', set())
        
        if extension in forbidden_extensions:
            return False
        
        if allowed_extensions and extension not in allowed_extensions:
            return False
        
        return True

def require_csrf(f):
    """ديكوريتر للتحقق من CSRF"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == 'POST':
            token = request.form.get('csrf_token') or request.headers.get('X-CSRF-Token')
            if not SecurityManager().validate_csrf_token(token):
                abort(403, 'CSRF token missing or invalid')
        return f(*args, **kwargs)
    return decorated_function

def rate_limit(max_requests=100, window=3600):
    """ديكوريتر لتحديد معدل الطلبات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # يمكن تطوير هذا لاحقاً مع Redis أو قاعدة بيانات
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            abort(403, 'Admin access required')
        return f(*args, **kwargs)
    return decorated_function

# إنشاء مثيل عام من مدير الأمان
security_manager = SecurityManager()
password_validator = PasswordValidator()
