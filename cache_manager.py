#!/usr/bin/env python3
"""
نظام إدارة التخزين المؤقت
Cache Management System
"""

import json
import time
import hashlib
import pickle
from datetime import datetime, timedelta
from functools import wraps
from pathlib import Path
import threading
import sqlite3
from flask import current_app, request

class CacheManager:
    """مدير التخزين المؤقت"""
    
    def __init__(self, cache_dir='cache', default_timeout=3600):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.default_timeout = default_timeout
        self.memory_cache = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        self.lock = threading.RLock()
        
        # إعداد قاعدة بيانات التخزين المؤقت
        self.db_path = self.cache_dir / 'cache.db'
        self._init_database()
    
    def _init_database(self):
        """تهيئة قاعدة بيانات التخزين المؤقت"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS cache_entries (
                        key TEXT PRIMARY KEY,
                        value BLOB,
                        expires_at REAL,
                        created_at REAL,
                        access_count INTEGER DEFAULT 0,
                        last_accessed REAL
                    )
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_expires_at ON cache_entries(expires_at)
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache_entries(last_accessed)
                ''')
                
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة بيانات التخزين المؤقت: {e}")
    
    def _generate_key(self, key_parts):
        """إنشاء مفتاح فريد من أجزاء متعددة"""
        if isinstance(key_parts, (list, tuple)):
            key_string = ':'.join(str(part) for part in key_parts)
        else:
            key_string = str(key_parts)
        
        # إضافة hash للمفاتيح الطويلة
        if len(key_string) > 200:
            return hashlib.md5(key_string.encode()).hexdigest()
        
        return key_string
    
    def get(self, key, default=None):
        """الحصول على قيمة من التخزين المؤقت"""
        key = self._generate_key(key)
        
        with self.lock:
            # البحث في الذاكرة أولاً
            if key in self.memory_cache:
                entry = self.memory_cache[key]
                if entry['expires_at'] > time.time():
                    self.cache_stats['hits'] += 1
                    entry['access_count'] += 1
                    entry['last_accessed'] = time.time()
                    return entry['value']
                else:
                    # انتهت صلاحية الإدخال
                    del self.memory_cache[key]
            
            # البحث في قاعدة البيانات
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute(
                        'SELECT value, expires_at, access_count FROM cache_entries WHERE key = ?',
                        (key,)
                    )
                    row = cursor.fetchone()
                    
                    if row:
                        value_blob, expires_at, access_count = row
                        
                        if expires_at > time.time():
                            # إدخال صالح
                            value = pickle.loads(value_blob)
                            
                            # تحديث إحصائيات الوصول
                            conn.execute('''
                                UPDATE cache_entries 
                                SET access_count = access_count + 1, last_accessed = ?
                                WHERE key = ?
                            ''', (time.time(), key))
                            
                            # إضافة للذاكرة للوصول السريع
                            self.memory_cache[key] = {
                                'value': value,
                                'expires_at': expires_at,
                                'access_count': access_count + 1,
                                'last_accessed': time.time()
                            }
                            
                            self.cache_stats['hits'] += 1
                            return value
                        else:
                            # حذف الإدخال المنتهي الصلاحية
                            conn.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                    
            except Exception as e:
                print(f"خطأ في قراءة التخزين المؤقت: {e}")
            
            self.cache_stats['misses'] += 1
            return default
    
    def set(self, key, value, timeout=None):
        """حفظ قيمة في التخزين المؤقت"""
        key = self._generate_key(key)
        timeout = timeout or self.default_timeout
        expires_at = time.time() + timeout
        created_at = time.time()
        
        with self.lock:
            try:
                # حفظ في قاعدة البيانات
                value_blob = pickle.dumps(value)
                
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute('''
                        INSERT OR REPLACE INTO cache_entries 
                        (key, value, expires_at, created_at, access_count, last_accessed)
                        VALUES (?, ?, ?, ?, 0, ?)
                    ''', (key, value_blob, expires_at, created_at, created_at))
                
                # حفظ في الذاكرة
                self.memory_cache[key] = {
                    'value': value,
                    'expires_at': expires_at,
                    'access_count': 0,
                    'last_accessed': created_at
                }
                
                self.cache_stats['sets'] += 1
                return True
                
            except Exception as e:
                print(f"خطأ في حفظ التخزين المؤقت: {e}")
                return False
    
    def delete(self, key):
        """حذف قيمة من التخزين المؤقت"""
        key = self._generate_key(key)
        
        with self.lock:
            # حذف من الذاكرة
            if key in self.memory_cache:
                del self.memory_cache[key]
            
            # حذف من قاعدة البيانات
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                    deleted = cursor.rowcount > 0
                
                if deleted:
                    self.cache_stats['deletes'] += 1
                
                return deleted
                
            except Exception as e:
                print(f"خطأ في حذف التخزين المؤقت: {e}")
                return False
    
    def clear(self):
        """مسح جميع البيانات المخزنة مؤقتاً"""
        with self.lock:
            # مسح الذاكرة
            self.memory_cache.clear()
            
            # مسح قاعدة البيانات
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute('DELETE FROM cache_entries')
                
                return True
                
            except Exception as e:
                print(f"خطأ في مسح التخزين المؤقت: {e}")
                return False
    
    def cleanup_expired(self):
        """تنظيف الإدخالات المنتهية الصلاحية"""
        current_time = time.time()
        
        with self.lock:
            # تنظيف الذاكرة
            expired_keys = [
                key for key, entry in self.memory_cache.items()
                if entry['expires_at'] <= current_time
            ]
            
            for key in expired_keys:
                del self.memory_cache[key]
            
            # تنظيف قاعدة البيانات
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute(
                        'DELETE FROM cache_entries WHERE expires_at <= ?',
                        (current_time,)
                    )
                    deleted_count = cursor.rowcount
                
                return deleted_count
                
            except Exception as e:
                print(f"خطأ في تنظيف التخزين المؤقت: {e}")
                return 0
    
    def get_stats(self):
        """الحصول على إحصائيات التخزين المؤقت"""
        with self.lock:
            total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
            hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            # إحصائيات قاعدة البيانات
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('SELECT COUNT(*) FROM cache_entries')
                    total_entries = cursor.fetchone()[0]
                    
                    cursor = conn.execute(
                        'SELECT COUNT(*) FROM cache_entries WHERE expires_at > ?',
                        (time.time(),)
                    )
                    active_entries = cursor.fetchone()[0]
                    
            except Exception:
                total_entries = 0
                active_entries = 0
            
            return {
                'hits': self.cache_stats['hits'],
                'misses': self.cache_stats['misses'],
                'sets': self.cache_stats['sets'],
                'deletes': self.cache_stats['deletes'],
                'hit_rate': round(hit_rate, 2),
                'memory_entries': len(self.memory_cache),
                'total_entries': total_entries,
                'active_entries': active_entries,
                'expired_entries': total_entries - active_entries
            }
    
    def get_size_info(self):
        """معلومات حجم التخزين المؤقت"""
        try:
            db_size = self.db_path.stat().st_size if self.db_path.exists() else 0
            
            # تقدير حجم الذاكرة
            memory_size = 0
            for entry in self.memory_cache.values():
                try:
                    memory_size += len(pickle.dumps(entry['value']))
                except:
                    pass
            
            return {
                'database_size': db_size,
                'database_size_formatted': self._format_size(db_size),
                'memory_size': memory_size,
                'memory_size_formatted': self._format_size(memory_size),
                'total_size': db_size + memory_size,
                'total_size_formatted': self._format_size(db_size + memory_size)
            }
            
        except Exception as e:
            print(f"خطأ في حساب حجم التخزين المؤقت: {e}")
            return {}
    
    def _format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"

def cached(timeout=3600, key_func=None):
    """ديكوريتر للتخزين المؤقت للدوال"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # إنشاء مفتاح التخزين المؤقت
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # مفتاح افتراضي بناءً على اسم الدالة والمعاملات
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
                cache_key = ':'.join(key_parts)
            
            # محاولة الحصول على القيمة من التخزين المؤقت
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # تنفيذ الدالة وحفظ النتيجة
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, timeout)
            
            return result
        
        return wrapper
    return decorator

def cache_page(timeout=3600):
    """ديكوريتر لتخزين الصفحات مؤقتاً"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # إنشاء مفتاح بناءً على المسار والمعاملات
            cache_key = f"page:{request.endpoint}:{request.path}"
            
            # إضافة معاملات الاستعلام
            if request.args:
                query_string = '&'.join(f"{k}={v}" for k, v in sorted(request.args.items()))
                cache_key += f"?{query_string}"
            
            # محاولة الحصول على الصفحة المخزنة
            cached_page = cache_manager.get(cache_key)
            if cached_page is not None:
                return cached_page
            
            # تنفيذ الدالة وحفظ النتيجة
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, timeout)
            
            return result
        
        return wrapper
    return decorator

def invalidate_cache(pattern):
    """إبطال التخزين المؤقت بناءً على نمط"""
    # يمكن تطوير هذه الدالة لاحقاً لدعم الأنماط المعقدة
    cache_manager.delete(pattern)

# إنشاء مثيل عام من مدير التخزين المؤقت
cache_manager = CacheManager()

# دالة تنظيف دورية
def cleanup_cache_periodically():
    """تنظيف التخزين المؤقت بشكل دوري"""
    import threading
    import time
    
    def cleanup_loop():
        while True:
            try:
                deleted_count = cache_manager.cleanup_expired()
                if deleted_count > 0:
                    print(f"تم تنظيف {deleted_count} إدخال منتهي الصلاحية من التخزين المؤقت")
                
                # تنظيف كل 30 دقيقة
                time.sleep(1800)
                
            except Exception as e:
                print(f"خطأ في تنظيف التخزين المؤقت: {e}")
                time.sleep(300)  # إعادة المحاولة بعد 5 دقائق
    
    cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
    cleanup_thread.start()

# بدء تنظيف التخزين المؤقت
cleanup_cache_periodically()
