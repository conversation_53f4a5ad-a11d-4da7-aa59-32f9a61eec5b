from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import Index, CheckConstraint, event
from sqlalchemy.orm import validates
import re

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدم لتسجيل الدخول"""
    __tablename__ = 'user'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)  # Increased length for bcrypt
    is_admin = db.Column(db.<PERSON>, default=False, nullable=False)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    last_login = db.Column(db.DateTime)
    failed_login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # إضافة قيود التحقق
    __table_args__ = (
        CheckConstraint('length(username) >= 3', name='username_min_length'),
        CheckConstraint('length(email) >= 5', name='email_min_length'),
        Index('idx_user_username_email', 'username', 'email'),
        Index('idx_user_active_admin', 'is_active', 'is_admin'),
    )

    @validates('username')
    def validate_username(self, key, username):
        if not username or len(username.strip()) < 3:
            raise ValueError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        if len(username) > 80:
            raise ValueError('اسم المستخدم طويل جداً')
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            raise ValueError('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط')
        return username.strip()

    @validates('email')
    def validate_email(self, key, email):
        if not email:
            raise ValueError('البريد الإلكتروني مطلوب')
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValueError('البريد الإلكتروني غير صحيح')
        return email.lower().strip()

    def set_password(self, password):
        """تشفير كلمة المرور"""
        from security import password_validator
        is_valid, errors = password_validator.validate(password)
        if not is_valid:
            raise ValueError('; '.join(errors))
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def is_locked(self):
        """التحقق من حظر المستخدم"""
        if self.locked_until and datetime.utcnow() < self.locked_until:
            return True
        return False

    def lock_account(self, duration_minutes=30):
        """حظر الحساب لفترة محددة"""
        from datetime import timedelta
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        db.session.commit()

    def unlock_account(self):
        """إلغاء حظر الحساب"""
        self.locked_until = None
        self.failed_login_attempts = 0
        db.session.commit()

    def __repr__(self):
        return f'<User {self.username}>'

class CapitalCategory(db.Model):
    """فئات رأس المال"""
    __tablename__ = 'capital_category'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    amount = db.Column(db.Integer, nullable=False, index=True)
    max_companies = db.Column(db.Integer, nullable=False, default=10)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقة مع الشركات
    companies = db.relationship('Company', backref='capital_category', lazy='dynamic',
                               cascade='all, delete-orphan')

    # إضافة قيود التحقق
    __table_args__ = (
        CheckConstraint('amount > 0', name='amount_positive'),
        CheckConstraint('max_companies > 0', name='max_companies_positive'),
        CheckConstraint('length(name) >= 3', name='name_min_length'),
        Index('idx_category_amount_active', 'amount', 'is_active'),
    )

    @validates('name')
    def validate_name(self, key, name):
        if not name or len(name.strip()) < 3:
            raise ValueError('اسم الفئة يجب أن يكون 3 أحرف على الأقل')
        if len(name) > 100:
            raise ValueError('اسم الفئة طويل جداً')
        return name.strip()

    @validates('amount')
    def validate_amount(self, key, amount):
        if not isinstance(amount, int) or amount <= 0:
            raise ValueError('مبلغ رأس المال يجب أن يكون رقماً موجباً')
        return amount

    @validates('max_companies')
    def validate_max_companies(self, key, max_companies):
        if not isinstance(max_companies, int) or max_companies <= 0:
            raise ValueError('الحد الأقصى للشركات يجب أن يكون رقماً موجباً')
        if max_companies > 1000:  # حد أقصى معقول
            raise ValueError('الحد الأقصى للشركات كبير جداً')
        return max_companies

    @property
    def current_companies_count(self):
        """عدد الشركات الحالية في هذه الفئة"""
        return self.companies.filter_by(is_active=True).count()

    @property
    def can_add_company(self):
        """هل يمكن إضافة شركة جديدة لهذه الفئة"""
        return self.is_active and self.current_companies_count < self.max_companies

    @property
    def usage_percentage(self):
        """نسبة الاستخدام"""
        if self.max_companies == 0:
            return 0
        return (self.current_companies_count / self.max_companies) * 100

    def __repr__(self):
        return f'<CapitalCategory {self.name}>'

class Person(db.Model):
    """نموذج الأشخاص (الملاك)"""
    __tablename__ = 'person'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    national_id = db.Column(db.String(20), unique=True, index=True)
    phone = db.Column(db.String(20), index=True)
    email = db.Column(db.String(120), index=True)
    address = db.Column(db.Text)
    notes = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقة مع الشركات كمالك أساسي
    owned_companies = db.relationship('Company', backref='primary_owner', lazy='dynamic')

    # إضافة قيود التحقق
    __table_args__ = (
        CheckConstraint('length(name) >= 2', name='name_min_length'),
        Index('idx_person_name_active', 'name', 'is_active'),
        Index('idx_person_national_id', 'national_id'),
    )

    @validates('name')
    def validate_name(self, key, name):
        if not name or len(name.strip()) < 2:
            raise ValueError('الاسم يجب أن يكون حرفين على الأقل')
        if len(name) > 100:
            raise ValueError('الاسم طويل جداً')
        # التحقق من وجود أحرف صحيحة فقط
        if not re.match(r'^[\u0600-\u06FFa-zA-Z\s]+$', name):
            raise ValueError('الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')
        return name.strip()

    @validates('national_id')
    def validate_national_id(self, key, national_id):
        if national_id:
            # إزالة المسافات والرموز
            national_id = re.sub(r'[^\d]', '', national_id)
            if len(national_id) != 10:
                raise ValueError('رقم الهوية يجب أن يكون 10 أرقام')
            if not national_id.startswith(('1', '2')):
                raise ValueError('رقم الهوية يجب أن يبدأ بـ 1 أو 2')
            # يمكن إضافة خوارزمية التحقق من رقم الهوية السعودية هنا
        return national_id

    @validates('phone')
    def validate_phone(self, key, phone):
        if phone:
            # إزالة المسافات والرموز
            phone = re.sub(r'[^\d]', '', phone)
            if len(phone) == 10 and phone.startswith('05'):
                return phone
            elif len(phone) == 12 and phone.startswith('9665'):
                return '0' + phone[3:]
            elif len(phone) == 9 and phone.startswith('5'):
                return '0' + phone
            else:
                raise ValueError('رقم الهاتف غير صحيح')
        return phone

    @validates('email')
    def validate_email(self, key, email):
        if email:
            email = email.lower().strip()
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                raise ValueError('البريد الإلكتروني غير صحيح')
        return email

    @property
    def companies_count(self):
        """عدد الشركات التي يملكها"""
        return self.owned_companies.filter_by(is_active=True).count()

    def __repr__(self):
        return f'<Person {self.name}>'

class Company(db.Model):
    """نموذج الشركات"""
    __tablename__ = 'company'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    project_plan = db.Column(db.Text, nullable=False)
    resources = db.Column(db.Text)
    work_method = db.Column(db.Text)

    # ربط مع فئة رأس المال
    capital_category_id = db.Column(db.Integer, db.ForeignKey('capital_category.id'),
                                   nullable=False, index=True)

    # المالك الأساسي
    primary_owner_id = db.Column(db.Integer, db.ForeignKey('person.id'),
                                nullable=False, index=True)

    # معلومات إضافية
    registration_number = db.Column(db.String(50), unique=True, index=True)
    license_number = db.Column(db.String(50), unique=True, index=True)
    establishment_date = db.Column(db.Date)

    # حالة الشركة
    is_active = db.Column(db.Boolean, default=True, nullable=False, index=True)
    status = db.Column(db.String(50), default='active', nullable=False, index=True)

    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    files = db.relationship('CompanyFile', backref='company', lazy='dynamic',
                           cascade='all, delete-orphan')
    additional_owners = db.relationship('CompanyOwnership', backref='company',
                                       lazy='dynamic', cascade='all, delete-orphan')

    # إضافة قيود التحقق
    __table_args__ = (
        CheckConstraint('length(name) >= 3', name='company_name_min_length'),
        CheckConstraint('length(project_plan) >= 10', name='project_plan_min_length'),
        CheckConstraint("status IN ('active', 'suspended', 'closed')", name='valid_status'),
        Index('idx_company_name_active', 'name', 'is_active'),
        Index('idx_company_category_status', 'capital_category_id', 'status'),
        Index('idx_company_owner_active', 'primary_owner_id', 'is_active'),
    )

    @validates('name')
    def validate_name(self, key, name):
        if not name or len(name.strip()) < 3:
            raise ValueError('اسم الشركة يجب أن يكون 3 أحرف على الأقل')
        if len(name) > 200:
            raise ValueError('اسم الشركة طويل جداً')
        # التحقق من عدم وجود أحرف خطيرة
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '{', '}']
        if any(char in name for char in dangerous_chars):
            raise ValueError('اسم الشركة يحتوي على أحرف غير مسموحة')
        return name.strip()

    @validates('project_plan')
    def validate_project_plan(self, key, project_plan):
        if not project_plan or len(project_plan.strip()) < 10:
            raise ValueError('خطة المشروع يجب أن تكون 10 أحرف على الأقل')
        return project_plan.strip()

    @validates('registration_number')
    def validate_registration_number(self, key, registration_number):
        if registration_number:
            registration_number = registration_number.strip()
            if len(registration_number) < 5:
                raise ValueError('رقم التسجيل قصير جداً')
        return registration_number

    @validates('status')
    def validate_status(self, key, status):
        valid_statuses = ['active', 'suspended', 'closed']
        if status not in valid_statuses:
            raise ValueError(f'حالة الشركة يجب أن تكون إحدى: {", ".join(valid_statuses)}')
        return status

    @property
    def files_count(self):
        """عدد الملفات المرفقة"""
        return self.files.count()

    @property
    def total_owners(self):
        """العدد الإجمالي للملاك (الأساسي + الإضافيين)"""
        return 1 + self.additional_owners.filter_by(is_active=True).count()

    def can_be_deleted(self):
        """التحقق من إمكانية حذف الشركة"""
        # لا يمكن حذف الشركة إذا كان لديها ملفات أو شركاء
        return self.files_count == 0 and self.additional_owners.count() == 0

    def __repr__(self):
        return f'<Company {self.name}>'

class CompanyOwnership(db.Model):
    """جدول ربط الملاك الإضافيين بالشركات"""
    __tablename__ = 'company_ownership'

    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False, index=True)
    person_id = db.Column(db.Integer, db.ForeignKey('person.id'), nullable=False, index=True)
    ownership_percentage = db.Column(db.Float, default=0.0, nullable=False)
    role = db.Column(db.String(100))
    start_date = db.Column(db.Date, default=datetime.utcnow, nullable=False)
    end_date = db.Column(db.Date)
    is_active = db.Column(db.Boolean, default=True, nullable=False, index=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    person = db.relationship('Person', backref='company_ownerships')

    # إضافة قيود التحقق
    __table_args__ = (
        CheckConstraint('ownership_percentage >= 0 AND ownership_percentage <= 100',
                       name='valid_ownership_percentage'),
        CheckConstraint('end_date IS NULL OR end_date >= start_date',
                       name='valid_date_range'),
        Index('idx_ownership_company_person', 'company_id', 'person_id'),
        Index('idx_ownership_active', 'is_active'),
        db.UniqueConstraint('company_id', 'person_id', name='unique_company_person'),
    )

    @validates('ownership_percentage')
    def validate_ownership_percentage(self, key, percentage):
        if percentage < 0 or percentage > 100:
            raise ValueError('نسبة الملكية يجب أن تكون بين 0 و 100')
        return percentage

    @validates('role')
    def validate_role(self, key, role):
        if role and len(role.strip()) < 2:
            raise ValueError('الدور يجب أن يكون حرفين على الأقل')
        return role.strip() if role else role

    def __repr__(self):
        return f'<CompanyOwnership {self.company.name} - {self.person.name}>'

class CompanyFile(db.Model):
    """ملفات الشركات"""
    __tablename__ = 'company_file'

    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False, index=True)
    filename = db.Column(db.String(255), nullable=False, index=True)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False, unique=True)
    file_type = db.Column(db.String(50), nullable=False, index=True)
    file_size = db.Column(db.Integer, nullable=False)
    mime_type = db.Column(db.String(100))
    file_hash = db.Column(db.String(64), index=True)  # SHA-256 hash للتحقق من التكرار
    description = db.Column(db.Text)
    is_public = db.Column(db.Boolean, default=False, nullable=False)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_accessed = db.Column(db.DateTime)
    access_count = db.Column(db.Integer, default=0, nullable=False)

    # العلاقات
    uploader = db.relationship('User', backref='uploaded_files')

    # إضافة قيود التحقق
    __table_args__ = (
        CheckConstraint('file_size > 0', name='file_size_positive'),
        CheckConstraint('access_count >= 0', name='access_count_non_negative'),
        CheckConstraint("file_type IN ('image', 'document', 'contract', 'other')",
                       name='valid_file_type'),
        Index('idx_file_company_type', 'company_id', 'file_type'),
        Index('idx_file_uploaded', 'uploaded_by', 'uploaded_at'),
    )

    @validates('original_filename')
    def validate_original_filename(self, key, filename):
        if not filename or len(filename.strip()) < 1:
            raise ValueError('اسم الملف مطلوب')
        # تنظيف اسم الملف
        from security import security_manager
        return security_manager.sanitize_filename(filename)

    @validates('file_size')
    def validate_file_size(self, key, file_size):
        if not isinstance(file_size, int) or file_size <= 0:
            raise ValueError('حجم الملف يجب أن يكون رقماً موجباً')
        max_size = 16 * 1024 * 1024  # 16MB
        if file_size > max_size:
            raise ValueError('حجم الملف كبير جداً')
        return file_size

    @validates('file_type')
    def validate_file_type(self, key, file_type):
        valid_types = ['image', 'document', 'contract', 'other']
        if file_type not in valid_types:
            raise ValueError(f'نوع الملف يجب أن يكون إحدى: {", ".join(valid_types)}')
        return file_type

    def record_access(self):
        """تسجيل الوصول للملف"""
        self.last_accessed = datetime.utcnow()
        self.access_count += 1
        db.session.commit()

    @property
    def formatted_size(self):
        """حجم الملف منسق"""
        from utils import format_file_size
        return format_file_size(self.file_size)

    def __repr__(self):
        return f'<CompanyFile {self.original_filename}>'

class SystemBackup(db.Model):
    """نسخ احتياطية للنظام"""
    __tablename__ = 'system_backup'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False, index=True)
    file_path = db.Column(db.String(500), nullable=False, unique=True)
    backup_type = db.Column(db.String(50), default='manual', nullable=False, index=True)
    file_size = db.Column(db.Integer, nullable=False)
    backup_hash = db.Column(db.String(64))  # للتحقق من سلامة النسخة
    status = db.Column(db.String(20), default='completed', nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    notes = db.Column(db.Text)

    # العلاقات
    creator = db.relationship('User', backref='created_backups')

    # إضافة قيود التحقق
    __table_args__ = (
        CheckConstraint("backup_type IN ('manual', 'automatic', 'scheduled')",
                       name='valid_backup_type'),
        CheckConstraint("status IN ('completed', 'failed', 'in_progress')",
                       name='valid_backup_status'),
        CheckConstraint('file_size > 0', name='backup_file_size_positive'),
        Index('idx_backup_type_date', 'backup_type', 'created_at'),
    )

    def __repr__(self):
        return f'<SystemBackup {self.filename}>'

class AuditLog(db.Model):
    """سجل المراجعة والتدقيق"""
    __tablename__ = 'audit_log'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    action = db.Column(db.String(100), nullable=False, index=True)
    resource_type = db.Column(db.String(50), nullable=False, index=True)
    resource_id = db.Column(db.Integer, index=True)
    old_values = db.Column(db.Text)  # JSON
    new_values = db.Column(db.Text)  # JSON
    ip_address = db.Column(db.String(45))  # IPv6 support
    user_agent = db.Column(db.String(500))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)

    # العلاقات
    user = db.relationship('User', backref='audit_logs')

    # إضافة قيود التحقق
    __table_args__ = (
        Index('idx_audit_user_action', 'user_id', 'action'),
        Index('idx_audit_resource', 'resource_type', 'resource_id'),
        Index('idx_audit_timestamp', 'timestamp'),
    )

    @validates('action')
    def validate_action(self, key, action):
        valid_actions = [
            'create', 'update', 'delete', 'login', 'logout',
            'upload', 'download', 'view', 'export'
        ]
        if action not in valid_actions:
            raise ValueError(f'العملية يجب أن تكون إحدى: {", ".join(valid_actions)}')
        return action

    @validates('resource_type')
    def validate_resource_type(self, key, resource_type):
        valid_types = [
            'user', 'company', 'person', 'capital_category',
            'company_file', 'system_backup'
        ]
        if resource_type not in valid_types:
            raise ValueError(f'نوع المورد يجب أن يكون إحدى: {", ".join(valid_types)}')
        return resource_type

    def __repr__(self):
        return f'<AuditLog {self.action} on {self.resource_type}>'

class LoginAttempt(db.Model):
    """محاولات تسجيل الدخول"""
    __tablename__ = 'login_attempt'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), index=True)
    ip_address = db.Column(db.String(45), nullable=False, index=True)
    user_agent = db.Column(db.String(500))
    success = db.Column(db.Boolean, nullable=False, index=True)
    failure_reason = db.Column(db.String(100))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)

    # إضافة قيود التحقق
    __table_args__ = (
        Index('idx_login_ip_time', 'ip_address', 'timestamp'),
        Index('idx_login_username_success', 'username', 'success'),
    )

    def __repr__(self):
        status = 'Success' if self.success else 'Failed'
        return f'<LoginAttempt {self.username} - {status}>'